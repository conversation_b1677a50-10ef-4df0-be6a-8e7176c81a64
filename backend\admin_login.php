<?php
require_once 'config.php';

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    json_response(['success' => false, 'message' => 'Invalid request method']);
}

// Get form data
$username = isset($_POST['username']) ? sanitize($_POST['username']) : '';
$password = isset($_POST['password']) ? $_POST['password'] : '';

// Validate form data
if (empty($username) || empty($password)) {
    json_response(['success' => false, 'message' => 'Please fill in all fields']);
}

try {
    // Get admin by username
    $stmt = $db->prepare("SELECT * FROM admins WHERE username = :username");
    $stmt->bindParam(':username', $username);
    $stmt->execute();
    
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin || !password_verify($password, $admin['password'])) {
        json_response(['success' => false, 'message' => 'Invalid username or password']);
    }
    
    // Set session variables
    $_SESSION['admin_id'] = $admin['id'];
    $_SESSION['admin_username'] = $admin['username'];
    
    // Return success response
    json_response(['success' => true, 'message' => 'Login successful']);
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error during login: ' . $e->getMessage()]);
}
