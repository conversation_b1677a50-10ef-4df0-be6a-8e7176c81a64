<?php
require_once 'config.php';

// Get category filter
$category = isset($_GET['category']) ? sanitize($_GET['category']) : 'all';

try {
    if ($category !== 'all') {
        $stmt = $db->prepare("SELECT * FROM products WHERE category = :category ORDER BY id DESC");
        $stmt->bindParam(':category', $category);
    } else {
        $stmt = $db->prepare("SELECT * FROM products ORDER BY id DESC");
    }
    
    $stmt->execute();
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    json_response($products);
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error fetching products: ' . $e->getMessage()]);
}