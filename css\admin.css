/* Dark mode variables */
:root {
  --admin-primary: #3498db;
  --admin-secondary: #2c3e50;
  --admin-bg: #f5f5f5;
  --admin-card-bg: #ffffff;
  --admin-border: #e0e0e0;
  --admin-text: #333333;
  --admin-accent: #3498db;
  --admin-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Dark mode variables */
.dark-mode {
  --admin-bg: #1a1a1a;
  --admin-card-bg: #252525;
  --admin-border: #333;
  --admin-text: #f0f0f0;
  --admin-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

/* Main layout structure */
.admin-container {
  display: flex;
  min-height: 100vh;
  flex-direction: row;
}

/* Vertical sidebar */
.sidebar {
  width: 250px;
  background-color: var(--admin-card-bg);
  border-right: 1px solid var(--admin-border);
  position: fixed;
  height: 100vh;
  overflow-y: auto;
  z-index: 10;
  box-shadow: 2px 0 10px rgba(0,0,0,0.2);
  display: flex;
  flex-direction: column;
  color: var(--admin-text);
  transition: all 0.3s ease;
}

.sidebar .logo {
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid var(--admin-border);
  margin-bottom: 15px;
}

.sidebar .logo h2 {
  color: var(--admin-primary);
  font-size: 1.8rem;
}

/* Vertical navigation */
.admin-nav {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.admin-nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.admin-nav li {
  width: 100%;
  margin: 0;
}

.admin-nav a {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  color: var(--admin-text);
  text-decoration: none;
  transition: all 0.3s ease;
  border-left: 3px solid transparent;
  width: 100%;
}

.admin-nav a i {
  margin-right: 10px;
  width: 20px;
  text-align: center;
}

.admin-nav a:hover {
  background-color: rgba(255,77,141,0.1);
  color: var(--admin-primary);
}

.admin-nav a.active {
  background-color: rgba(255,77,141,0.2);
  border-left: 3px solid var(--admin-primary);
  color: var(--admin-primary);
}

/* Main content area */
.admin-main {
  flex: 1;
  margin-left: 250px;
  padding: 25px;
  background-color: var(--admin-bg);
  min-height: 100vh;
  color: var(--admin-text);
  transition: all 0.3s ease;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--admin-border);
}

.admin-header-right {
  display: flex;
  align-items: center;
}

.admin-user {
  margin-left: 15px;
  font-weight: 500;
}

/* Horizontal stats cards */
.stats-grid {
  display: flex; /* Changed to flex for horizontal layout */
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 35px;
}

.stat-card {
  flex: 1;
  min-width: 200px;
  background-color: var(--admin-card-bg);
  border-radius: 10px;
  padding: 25px;
  box-shadow: 0 4px 15px rgba(0,0,0,0.2);
  border: 1px solid var(--admin-border);
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-card h3 {
  font-size: 1rem;
  margin-bottom: 15px;
  color: #aaa;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.stat-value {
  font-size: 2.2rem;
  font-weight: bold;
  color: var(--admin-primary);
}

/* Tables */
.admin-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;
  background-color: var(--admin-card-bg);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--admin-shadow);
}

.admin-table th,
.admin-table td {
  padding: 15px;
  text-align: left;
  border-bottom: 1px solid var(--admin-border);
}

.admin-table th {
  background-color: var(--admin-card-bg);
  font-weight: 600;
  color: var(--admin-text);
}

.admin-table tr:last-child td {
  border-bottom: none;
}

.admin-table tr:hover td {
  background-color: rgba(0,0,0,0.02);
}

.dark-mode .admin-table tr:hover td {
  background-color: rgba(255,255,255,0.05);
}

/* Section headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

/* Buttons */
.admin-btn {
  display: inline-block;
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
  text-decoration: none;
}

.admin-btn-primary {
  background-color: var(--admin-primary);
  color: white;
}

.admin-btn-secondary {
  background-color: var(--admin-secondary);
  color: white;
}

.admin-btn-danger {
  background-color: #ff3860;
  color: white;
}

.admin-btn-success {
  background-color: #28a745;
  color: white;
}

.admin-btn-warning {
  background-color: #ffc107;
  color: #212529;
}

/* Enhanced Delivery Page Styles */
.delivery-header {
  margin-bottom: 30px;
}

.delivery-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 15px;
  padding: 25px;
  color: white;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-card:nth-child(2) {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.stat-card:nth-child(3) {
  background: linear-gradient(135deg, #fc466b 0%, #3f5efb 100%);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.9;
}

.stat-info h4 {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
  margin-bottom: 5px;
}

.stat-info p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

/* Enhanced Admin Actions */
.admin-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 15px;
}

.actions-left, .actions-right {
  display: flex;
  gap: 10px;
  align-items: center;
}

/* Enhanced Card Styles */
.enhanced-card {
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  border: none;
}

.enhanced-card .admin-card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 30px;
  border-bottom: none;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 20px;
}

.header-title h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-title p {
  margin: 5px 0 0 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

.header-actions {
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
}

/* Search Box */
.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: #666;
  z-index: 1;
}

.search-box input {
  padding: 10px 15px 10px 35px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 0.9rem;
  width: 200px;
  transition: all 0.3s ease;
}

.search-box input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.search-box input:focus {
  outline: none;
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  width: 250px;
}

/* Filter Buttons */
.filter-buttons {
  display: flex;
  gap: 5px;
}

.filter-btn {
  padding: 8px 15px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  background: transparent;
  color: white;
  border-radius: 20px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover, .filter-btn.active {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

/* Enhanced Table */
.table-container {
  overflow-x: auto;
  border-radius: 15px;
}

.enhanced-table {
  border-radius: 15px;
  overflow: hidden;
}

.enhanced-table th {
  background: #f8f9fa;
  color: #495057;
  font-weight: 600;
  padding: 20px 15px;
  border-bottom: 2px solid #dee2e6;
  position: relative;
}

.enhanced-table th.sortable {
  cursor: pointer;
  user-select: none;
  transition: background-color 0.3s ease;
}

.enhanced-table th.sortable:hover {
  background: #e9ecef;
}

.enhanced-table th span {
  display: block;
  font-size: 0.9rem;
}

.enhanced-table th small {
  display: block;
  font-size: 0.75rem;
  opacity: 0.7;
  font-weight: normal;
}

.enhanced-table th i {
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0.5;
  transition: opacity 0.3s ease;
}

.enhanced-table th.sortable:hover i {
  opacity: 1;
}

.enhanced-table td {
  padding: 15px;
  vertical-align: middle;
  border-bottom: 1px solid #f1f3f4;
}

.enhanced-table tbody tr {
  transition: background-color 0.3s ease;
}

.enhanced-table tbody tr:hover {
  background-color: #f8f9fa;
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 40px;
  color: #6c757d;
}

.loading-spinner i {
  font-size: 1.2rem;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.status-badge.inactive {
  background: #f8d7da;
  color: #721c24;
}

/* Responsive Design */
@media (max-width: 768px) {
  .delivery-stats {
    grid-template-columns: 1fr;
  }

  .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .header-actions {
    justify-content: center;
  }

  .admin-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .actions-left, .actions-right {
    justify-content: center;
  }

  .search-box input {
    width: 100%;
  }

  .search-box input:focus {
    width: 100%;
  }
}

.admin-btn:hover {
  opacity: 0.9;
  transform: translateY(-2px);
}

.btn-sm {
  padding: 5px 10px;
  font-size: 0.9rem;
}

.admin-actions {
  margin-bottom: 20px;
}

.view-all {
  text-align: center;
  margin-top: 20px;
}

/* Forms */
.admin-form-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.admin-form {
  background-color: var(--admin-card-bg);
  border-radius: 8px;
  padding: 30px;
  width: 100%;
  max-width: 600px;
  box-shadow: var(--shadow);
}

.admin-form h2 {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--admin-border);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--admin-border);
  border-radius: 4px;
  background-color: var(--admin-card-bg);
  color: var(--admin-text);
}

.form-group textarea {
  min-height: 100px;
  resize: vertical;
}

.form-group small {
  display: block;
  margin-top: 5px;
  color: #777;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* Status badges */
.status-badge {
  display: inline-block;
  padding: 3px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
}

.status-pending {
  background-color: #ffdd57;
  color: #806600;
}

.status-processing {
  background-color: #3298dc;
  color: white;
}

.status-completed {
  background-color: #48c774;
  color: white;
}

.status-cancelled {
  background-color: #ff3860;
  color: white;
}

/* Product thumbnail */
.product-thumbnail {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
}

/* Status select */
.status-select {
  padding: 5px;
  border-radius: 4px;
  border: 1px solid var(--admin-border);
  background-color: var(--admin-card-bg);
  color: var(--admin-text);
}

/* Modal */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: var(--admin-card-bg);
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid var(--admin-border);
}

.modal-body {
  padding: 20px;
}

.close-modal {
  font-size: 1.5rem;
  cursor: pointer;
}

/* Order details */
.order-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.order-info,
.customer-info {
  background-color: var(--admin-card-bg);
  border-radius: 8px;
  padding: 20px;
}

.order-info h3,
.customer-info h3,
.order-items h3 {
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--admin-border);
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-label {
  font-weight: 500;
  width: 120px;
}

/* Login page */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
}

.login-form {
  background-color: var(--admin-card-bg);
  border-radius: 8px;
  padding: 30px;
  width: 100%;
  max-width: 400px;
  box-shadow: var(--shadow);
}

.login-form .logo {
  text-align: center;
  margin-bottom: 30px;
}

.login-form .logo h1 {
  margin-bottom: 5px;
}

.login-form .logo p {
  color: var(--admin-primary);
}

/* Responsive */
@media (max-width: 768px) {
  .admin-container {
    grid-template-columns: 1fr;
  }
    
  .sidebar {
    position: static;
    width: 100%;
    height: auto;
    margin-bottom: 20px;
  }
    
  .admin-main {
    margin-left: 0;
  }
    
  .order-details {
    grid-template-columns: 1fr;
  }
}

/* Notification styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 4px;
  color: white;
  font-weight: 500;
  z-index: 1000;
  transform: translateY(-100px);
  opacity: 0;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.notification.show {
  transform: translateY(0);
  opacity: 1;
}

.notification.success {
  background-color: #48c774;
}

.notification.error {
  background-color: #ff3860;
}

/* Make sure body has proper styling */
body.admin-page {
  margin: 0;
  padding: 0;
  background-color: var(--admin-bg);
  color: var(--admin-text);
  min-height: 100vh;
  transition: all 0.3s ease;
}

/* Apply dark mode to html element for full page coverage */
html.dark-mode {
  background-color: var(--admin-bg);
}

/* Theme toggle button */
#theme-toggle {
  background: none;
  border: none;
  color: var(--admin-text);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 5px;
  transition: all 0.3s ease;
}

#theme-toggle:hover {
  color: var(--admin-primary);
}

/* Product form styling */
.admin-form-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.admin-form {
  background-color: var(--admin-card-bg);
  border-radius: 8px;
  padding: 25px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
  border: 1px solid var(--admin-border);
}

.admin-form h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: var(--admin-primary);
  font-size: 1.5rem;
  border-bottom: 1px solid var(--admin-border);
  padding-bottom: 10px;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
}

.form-row .form-group {
  flex: 1;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 10px;
  border: 1px solid var(--admin-border);
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  color: var(--admin-text);
  font-size: 0.9rem;
}

.form-group textarea {
  resize: vertical;
}

/* Image upload styling */
.image-upload-container {
  margin-bottom: 20px;
}

.image-upload-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
}

.image-preview {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px dashed var(--admin-border);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.upload-controls {
  flex: 1;
}

.upload-controls input[type="file"] {
  display: none;
}

.upload-btn {
  display: inline-block;
  padding: 8px 15px;
  background-color: var(--admin-secondary);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 0.9rem;
}

.upload-btn:hover {
  background-color: #7b27c7;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.admin-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.admin-btn-primary {
  background-color: var(--admin-primary);
  color: white;
}

.admin-btn-secondary {
  background-color: #555;
  color: white;
}

.admin-btn-primary:hover {
  background-color: #e6397a;
}

.admin-btn-secondary:hover {
  background-color: #444;
}

/* Beautiful Notification Styles */
#notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.beautiful-notification {
  display: flex;
  align-items: flex-start;
  width: 350px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  position: relative;
  transform: translateX(120%);
  transition: transform 0.3s ease;
  margin-bottom: 10px;
}

.beautiful-notification.active {
  transform: translateX(0);
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 100%;
  padding: 15px 0;
  font-size: 24px;
}

.beautiful-notification.success .notification-icon {
  color: #4CAF50;
}

.beautiful-notification.error .notification-icon {
  color: #F44336;
}

.beautiful-notification.info .notification-icon {
  color: #2196F3;
}

.beautiful-notification.warning .notification-icon {
  color: #FF9800;
}

.notification-content {
  flex: 1;
  padding: 15px 10px;
}

.notification-content h4 {
  margin: 0 0 5px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.notification-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.notification-close {
  background: none;
  border: none;
  color: #999;
  font-size: 16px;
  cursor: pointer;
  padding: 15px;
  margin-left: auto;
}

.notification-close:hover {
  color: #333;
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
}

.beautiful-notification.success .notification-progress::before {
  background-color: #4CAF50;
}

.beautiful-notification.error .notification-progress::before {
  background-color: #F44336;
}

.beautiful-notification.info .notification-progress::before {
  background-color: #2196F3;
}

.beautiful-notification.warning .notification-progress::before {
  background-color: #FF9800;
}

.notification-progress::before {
  content: '';
  position: absolute;
  height: 100%;
  width: 100%;
  transform: scaleX(0);
  transform-origin: left;
}

@keyframes notification-progress {
  0% {
    transform: scaleX(1);
  }
  100% {
    transform: scaleX(0);
  }
}

/* Dark mode styles for notifications */
.dark-mode .beautiful-notification {
  background-color: #333;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.dark-mode .notification-content h4 {
  color: #fff;
}

.dark-mode .notification-content p {
  color: #ccc;
}

.dark-mode .notification-close {
  color: #aaa;
}

.dark-mode .notification-close:hover {
  color: #fff;
}

.dark-mode .notification-progress {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Loading spinner */
.loading-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid #4CAF50;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modal styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background-color: #fff;
  border-radius: 8px;
  width: 90%;
  max-width: 1000px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  position: relative;
}

.dark-mode .modal-content {
  background-color: #333;
  color: #fff;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.dark-mode .modal-header {
  border-bottom: 1px solid #444;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.close-modal {
  font-size: 24px;
  cursor: pointer;
  color: #777;
  transition: color 0.2s ease;
}

.close-modal:hover {
  color: #333;
}

.dark-mode .close-modal {
  color: #aaa;
}

.dark-mode .close-modal:hover {
  color: #fff;
}

.modal-body {
  padding: 20px;
}

/* Order details styles */
.order-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

@media (max-width: 768px) {
  .order-details {
    grid-template-columns: 1fr;
  }
}

.order-info, .customer-info {
  background-color: #f9f9f9;
  border-radius: 8px;
  padding: 15px;
}

.dark-mode .order-info, 
.dark-mode .customer-info {
  background-color: #444;
}

.order-info h3, .customer-info h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 1.2rem;
  border-bottom: 1px solid #eee;
  padding-bottom: 10px;
}

.dark-mode .order-info h3, 
.dark-mode .customer-info h3 {
  border-bottom: 1px solid #555;
}

.info-row {
  display: flex;
  margin-bottom: 10px;
}

.info-label {
  font-weight: 600;
  width: 120px;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
}

.status-select {
  padding: 5px 10px;
  border-radius: 4px;
  border: 1px solid #ddd;
  margin-right: 10px;
}

.dark-mode .status-select {
  background-color: #333;
  color: #fff;
  border-color: #555;
}

/* Fix for order items table */
.order-items {
  overflow-x: auto;
}

.order-items table {
  width: 100%;
  border-collapse: collapse;
}

.order-items th, 
.order-items td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.dark-mode .order-items th, 
.dark-mode .order-items td {
  border-bottom: 1px solid #444;
}

.order-items th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.dark-mode .order-items th {
  background-color: #3a3a3a;
}

.order-items tfoot td {
  font-weight: 600;
}
  font-size: 0.9rem;
}

.form-group textarea {
  resize: vertical;
}

/* Image upload styling */
.image-upload-container {
  margin-bottom: 20px;
}

.image-upload-wrapper {
  display: flex;
  align-items: center;
  gap: 15px;
}

.image-preview {
  width: 100px;
  height: 100px;
  border-radius: 4px;
  background-color: rgba(0, 0, 0, 0.2);
  border: 1px dashed var(--admin-border);
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: cover;
}

.upload-controls {
  flex: 1;
}

.upload-controls input[type="file"] {
  display: none;
}

.upload-btn {
  display: inline-block;
  padding: 8px 15px;
  background-color: var(--admin-secondary);
  color: white;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
  font-size: 0.9rem;
}

.upload-btn:hover {
  background-color: #7b27c7;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

.admin-btn {
  padding: 8px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s;
}

.admin-btn-primary {
  background-color: var(--admin-primary);
  color: white;
}

.admin-btn-secondary {
  background-color: #555;
  color: white;
}

.admin-btn-primary:hover {
  background-color: #e6397a;
}

.admin-btn-secondary:hover {
  background-color: #444;
}

/* Beautiful Notification Styles */
#notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.beautiful-notification {
  display: flex;
  align-items: flex-start;
  width: 350px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  position: relative;
  transform: translateX(120%);
  transition: transform 0.3s ease;
  margin-bottom: 10px;
}

.beautiful-notification.active {
  transform: translateX(0);
}

.notification-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 100%;
  padding: 15px 0;
  font-size: 24px;
}

.beautiful-notification.success .notification-icon {
  color: #4CAF50;
}

.beautiful-notification.error .notification-icon {
  color: #F44336;
}

.beautiful-notification.info .notification-icon {
  color: #2196F3;
}

.beautiful-notification.warning .notification-icon {
  color: #FF9800;
}

.notification-content {
  flex: 1;
  padding: 15px 10px;
}

.notification-content h4 {
  margin: 0 0 5px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.notification-content p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

.notification-close {
  background: none;
  border: none;
  color: #999;
  font-size: 16px;
  cursor: pointer;
  padding: 15px;
  margin-left: auto;
}

.notification-close:hover {
  color: #333;
}

.notification-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 3px;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.1);
}

.beautiful-notification.success .notification-progress::before {
  background-color: #4CAF50;
}

.beautiful-notification.error .notification-progress::before {
  background-color: #F44336;
}

.beautiful-notification.info .notification-progress::before {
  background-color: #2196F3;
}

.beautiful-notification.warning .notification-progress::before {
  background-color: #FF9800;
}

.notification-progress::before {
  content: '';
  position: absolute;
  height: 100%;
  width: 100%;
  transform: scaleX(0);
  transform-origin: left;
}

@keyframes notification-progress {
  0% {
    transform: scaleX(1);
  }
  100% {
    transform: scaleX(0);
  }
}

/* Dark mode styles for delivery page specific elements */
.dark-mode .delivery-settings {
  background-color: var(--admin-bg);
}

.dark-mode .admin-actions {
  background-color: var(--admin-bg);
}

.dark-mode .admin-actions .admin-btn {
  color: var(--admin-text);
}

/* Ensure all admin elements inherit dark mode properly */
.dark-mode .admin-card,
.dark-mode .admin-card-header,
.dark-mode .admin-card-body {
  background-color: var(--admin-card-bg) !important;
  color: var(--admin-text) !important;
  border-color: var(--admin-border) !important;
}

.dark-mode .admin-table {
  background-color: var(--admin-card-bg) !important;
  color: var(--admin-text) !important;
}

.dark-mode .admin-table th {
  background-color: #3a3a3a !important;
  color: var(--admin-text) !important;
  border-color: var(--admin-border) !important;
}

.dark-mode .admin-table td {
  color: var(--admin-text) !important;
  border-color: var(--admin-border) !important;
}

.dark-mode .admin-table tbody tr:hover td {
  background-color: rgba(255,255,255,0.05) !important;
}



