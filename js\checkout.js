// Load cart items
function loadCartItems() {
    const cartItemsContainer = document.getElementById('cart-items');
    const cartSubtotalElement = document.getElementById('cart-subtotal');
    const cartShippingElement = document.getElementById('shipping-cost');
    const cartTotalElement = document.getElementById('cart-total');
    
    if (!cartItemsContainer) return;
    
    // Get cart items
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    if (cart.length === 0) {
        cartItemsContainer.innerHTML = '<div class="empty-cart">Your cart is empty</div>';
        if (cartSubtotalElement) cartSubtotalElement.textContent = '0.00 DZD';
        if (cartShippingElement) cartShippingElement.textContent = '0.00 DZD';
        if (cartTotalElement) cartTotalElement.textContent = '0.00 DZD';
        return;
    }
    
    // Generate cart items HTML
    let html = '';
    let subtotal = 0;
    
    cart.forEach(item => {
        const itemTotal = parseFloat(item.price) * item.quantity;
        subtotal += itemTotal;
        
        html += `
            <div class="checkout-item">
                <div class="checkout-item-img">
                    <img src="${item.image}" alt="${item.name}" width="70" height="70">
                </div>
                <div class="checkout-item-info">
                    <h4>${item.name}</h4>
                    <div class="checkout-item-price">${item.price} DZD x ${item.quantity}</div>
                    <div class="checkout-item-total">${itemTotal.toFixed(2)} DZD</div>
                </div>
            </div>
        `;
    });
    
    cartItemsContainer.innerHTML = html;
    
    // Update subtotal
    if (cartSubtotalElement) {
        cartSubtotalElement.textContent = subtotal.toFixed(2) + ' DZD';
    }
    
    // Update shipping cost and total
    updateShippingCost();
}

// Update shipping cost
function updateShippingCost() {
    const wilayaSelect = document.getElementById('wilaya');
    const deliveryTypeRadios = document.querySelectorAll('input[name="delivery-type"]');
    const cartSubtotalElement = document.getElementById('cart-subtotal');
    const cartShippingElement = document.getElementById('shipping-cost');
    const cartTotalElement = document.getElementById('cart-total');
    
    if (!wilayaSelect || !cartSubtotalElement || !cartShippingElement || !cartTotalElement) {
        console.error('Required elements not found');
        return;
    }
    
    // Get selected delivery type
    let deliveryType = '';
    deliveryTypeRadios.forEach(radio => {
        if (radio.checked) {
            deliveryType = radio.value;
        }
    });
    
    // Get selected wilaya code
    const wilayaCode = wilayaSelect.value;
    
    console.log('Selected wilaya:', wilayaCode);
    console.log('Delivery type:', deliveryType);
    
    if (!wilayaCode) {
        // No wilaya selected
        cartShippingElement.textContent = '0.00 DZD';
        
        // Update total with just subtotal
        const subtotalText = cartSubtotalElement.textContent;
        const subtotal = parseFloat(subtotalText.replace(' DZD', '')) || 0;
        cartTotalElement.textContent = subtotal.toFixed(2) + ' DZD';
        return;
    }
    
    // Fetch delivery cost from backend
    fetch(`backend/get_delivery_cost.php?wilaya=${wilayaCode}`)
        .then(response => response.json())
        .then(data => {
            console.log('Delivery cost data:', data);
            
            let shippingCost = 0;
            
            if (data.success) {
                if (deliveryType === 'domicile') {
                    shippingCost = parseInt(data.domicile) || 0;
                } else if (deliveryType === 'stopdesk') {
                    shippingCost = parseInt(data.stopdesk) || 0;
                }
            } else {
                console.error('Error fetching delivery cost:', data.message);
                // Use default values
                shippingCost = (deliveryType === 'domicile') ? 1000 : 700;
            }
            
            console.log('Shipping cost:', shippingCost);
            
            // Update shipping cost
            cartShippingElement.textContent = shippingCost.toFixed(2) + ' DZD';
            
            // Update total
            const subtotalText = cartSubtotalElement.textContent;
            const subtotal = parseFloat(subtotalText.replace(' DZD', '')) || 0;
            const total = subtotal + shippingCost;
            
            console.log('Subtotal:', subtotal);
            console.log('Total:', total);
            
            cartTotalElement.textContent = total.toFixed(2) + ' DZD';
        })
        .catch(error => {
            console.error('Error fetching delivery cost:', error);
            
            // Use default values
            const shippingCost = (deliveryType === 'domicile') ? 1000 : 700;
            
            // Update shipping cost
            cartShippingElement.textContent = shippingCost.toFixed(2) + ' DZD';
            
            // Update total
            const subtotalText = cartSubtotalElement.textContent;
            const subtotal = parseFloat(subtotalText.replace(' DZD', '')) || 0;
            const total = subtotal + shippingCost;
            
            cartTotalElement.textContent = total.toFixed(2) + ' DZD';
        });
}

// Load wilaya options from database
function loadWilayaOptions() {
    const wilayaSelect = document.getElementById('wilaya');
    
    if (!wilayaSelect) {
        console.error('Wilaya select element not found');
        return;
    }
    
    console.log('Loading wilaya options...');
    
    fetch('backend/get_delivery_costs_public.php')
        .then(response => response.json())
        .then(data => {
            console.log('Wilaya data received:', data);
            
            let options = '<option value="">Select Wilaya</option>';
            
            data.forEach(item => {
                options += `<option value="${item.wilaya_code}" 
                                   data-domicile="${item.domicile}" 
                                   data-stopdesk="${item.stopdesk}">
                                ${item.wilaya_name}
                           </option>`;
            });
            
            wilayaSelect.innerHTML = options;
            
            // Update shipping cost after loading wilaya options
            updateShippingCost();
        })
        .catch(error => {
            console.error('Error loading wilaya options:', error);
            
            // Use fallback wilaya data
            const wilayaData = [
                { code: '1', name: 'ADRAR', domicile: 1350, stopdesk: 1070 },
                { code: '2', name: 'CHLEF', domicile: 850, stopdesk: 570 },
                { code: '3', name: 'LAGHOUAT', domicile: 950, stopdesk: 670 },
                { code: '4', name: 'OUM EL BOUAGHI', domicile: 800, stopdesk: 570 },
                { code: '5', name: 'BATNA', domicile: 800, stopdesk: 570 },
                { code: '6', name: 'BEJAIA', domicile: 800, stopdesk: 570 },
                { code: '7', name: 'BISKRA', domicile: 950, stopdesk: 670 },
                { code: '8', name: 'BECHAR', domicile: 1300, stopdesk: 870 },
                { code: '9', name: 'BLIDA', domicile: 600, stopdesk: 400 },
                { code: '10', name: 'BOUIRA', domicile: 700, stopdesk: 500 },
                { code: '16', name: 'ALGER', domicile: 500, stopdesk: 350 },
                { code: '31', name: 'ORAN', domicile: 800, stopdesk: 550 }
            ];
            
            let options = '<option value="">Select Wilaya</option>';
            
            wilayaData.forEach(item => {
                options += `<option value="${item.code}" 
                                   data-domicile="${item.domicile}" 
                                   data-stopdesk="${item.stopdesk}">
                                ${item.name}
                           </option>`;
            });
            
            wilayaSelect.innerHTML = options;
            
            // Update shipping cost after loading wilaya options
            updateShippingCost();
        });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Checkout page loaded');
    
    // Load wilaya options
    loadWilayaOptions();
    
    // Load cart items
    loadCartItems();
    
    // Handle wilaya and delivery type change
    const wilayaSelect = document.getElementById('wilaya');
    const deliveryTypeRadios = document.querySelectorAll('input[name="delivery-type"]');
    
    if (wilayaSelect) {
        wilayaSelect.addEventListener('change', function() {
            console.log('Wilaya changed:', this.value);
            updateShippingCost();
            this.classList.remove('error-field');
        });
    }
    
    if (deliveryTypeRadios.length > 0) {
        deliveryTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                console.log('Delivery type changed:', this.value);
                updateShippingCost();
            });
        });
    }
    
    // Add input event listeners to remove error class when user types
    const nameInput = document.getElementById('name');
    const phoneInput = document.getElementById('phone');
    const addressInput = document.getElementById('address');
    
    if (nameInput) {
        nameInput.addEventListener('input', function() {
            this.classList.remove('error-field');
        });
    }
    
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            this.classList.remove('error-field');
        });
    }
    
    if (addressInput) {
        addressInput.addEventListener('input', function() {
            this.classList.remove('error-field');
        });
    }
    
    // Theme toggle functionality
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;
    
    if (themeToggle) {
        // Check for saved theme preference
        if (localStorage.getItem('theme') === 'dark') {
            body.classList.add('dark-mode');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        } else {
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }
        
        themeToggle.addEventListener('click', function() {
            body.classList.toggle('dark-mode');
            
            if (body.classList.contains('dark-mode')) {
                localStorage.setItem('theme', 'dark');
                themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                localStorage.setItem('theme', 'light');
                themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });
    } else {
        // Apply dark mode if saved, even if toggle button is not present
        if (localStorage.getItem('theme') === 'dark') {
            body.classList.add('dark-mode');
        }
    }
    
    // Handle checkout form submission
    const checkoutForm = document.getElementById('checkout-form');
    
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', handleCheckoutSubmission);
    }
});

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    
    if (notification) {
        notification.textContent = message;
        notification.className = 'notification show';
        
        if (type === 'error') {
            notification.classList.add('error');
        } else if (type === 'info') {
            notification.classList.add('info');
        }
        
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    } else {
        console.log('Notification:', message, type);
    }
}

// Handle checkout form submission
function handleCheckoutSubmission(e) {
    e.preventDefault();
    
    // Get cart items
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    if (cart.length === 0) {
        showNotification('Your cart is empty', 'error');
        return;
    }
    
    // Validate form fields
    const name = document.getElementById('name').value.trim();
    const phone = document.getElementById('phone').value.trim();
    const wilaya = document.getElementById('wilaya').value.trim();
    const address = document.getElementById('address').value.trim();
    
    // Check required fields
    let missingFields = [];
    
    if (!name) {
        missingFields.push('Full Name');
        document.getElementById('name').classList.add('error-field');
    } else {
        document.getElementById('name').classList.remove('error-field');
    }
    
    if (!phone) {
        missingFields.push('Phone Number');
        document.getElementById('phone').classList.add('error-field');
    } else {
        document.getElementById('phone').classList.remove('error-field');
    }
    
    if (!wilaya) {
        missingFields.push('Wilaya');
        document.getElementById('wilaya').classList.add('error-field');
    } else {
        document.getElementById('wilaya').classList.remove('error-field');
    }
    
    if (!address) {
        missingFields.push('Address');
        document.getElementById('address').classList.add('error-field');
    } else {
        document.getElementById('address').classList.remove('error-field');
    }
    
    // Show error if any required fields are missing
    if (missingFields.length > 0) {
        showNotification('Missing required fields: ' + missingFields.join(', '), 'error');
        return;
    }
    
    // Get selected delivery type
    let deliveryType = '';
    const deliveryTypeRadios = document.querySelectorAll('input[name="delivery-type"]');
    deliveryTypeRadios.forEach(radio => {
        if (radio.checked) {
            deliveryType = radio.value;
        }
    });
    
    // Get form data
    const formData = new FormData(document.getElementById('checkout-form'));
    
    // Add cart items to form data
    formData.append('cart', JSON.stringify(cart));
    
    // Add delivery type to form data
    formData.append('delivery_type', deliveryType);
    
    // Show loading indicator
    const submitBtn = document.querySelector('button[type="submit"]');
    const originalBtnText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    submitBtn.disabled = true;
    
    // Submit order
    fetch('backend/process_order.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        // Reset button
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
        
        if (data.success) {
            // Clear cart
            localStorage.removeItem('cart');
            
            // Show success message
            showNotification('Order placed successfully');
            
            // Redirect to confirmation page
            setTimeout(() => {
                window.location.href = 'confirmation.html?order_id=' + data.order_id;
            }, 2000);
        } else {
            showNotification(data.message || 'Failed to place order', 'error');
        }
    })
    .catch(error => {
        // Reset button
        submitBtn.innerHTML = originalBtnText;
        submitBtn.disabled = false;
        
        console.error('Error placing order:', error);
        showNotification('Failed to place order', 'error');
    });
}

document.addEventListener('DOMContentLoaded', function() {
    console.log('Checkout page loaded');
    
    // Load wilaya options
    loadWilayaOptions();
    
    // Load cart items
    loadCartItems();
    
    // Handle wilaya and delivery type change
    const wilayaSelect = document.getElementById('wilaya');
    const deliveryTypeRadios = document.querySelectorAll('input[name="delivery-type"]');
    
    if (wilayaSelect) {
        wilayaSelect.addEventListener('change', function() {
            console.log('Wilaya changed:', this.value);
            updateShippingCost();
            this.classList.remove('error-field');
        });
    }
    
    if (deliveryTypeRadios.length > 0) {
        deliveryTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                console.log('Delivery type changed:', this.value);
                updateShippingCost();
            });
        });
    }
    
    // Add input event listeners to remove error class when user types
    const nameInput = document.getElementById('name');
    const phoneInput = document.getElementById('phone');
    const addressInput = document.getElementById('address');
    
    if (nameInput) {
        nameInput.addEventListener('input', function() {
            this.classList.remove('error-field');
        });
    }
    
    if (phoneInput) {
        phoneInput.addEventListener('input', function() {
            this.classList.remove('error-field');
        });
    }
    
    if (addressInput) {
        addressInput.addEventListener('input', function() {
            this.classList.remove('error-field');
        });
    }
    
    // Theme toggle functionality
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;
    
    if (themeToggle) {
        // Check for saved theme preference
        if (localStorage.getItem('theme') === 'dark') {
            body.classList.add('dark-mode');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        } else {
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }
        
        themeToggle.addEventListener('click', function() {
            body.classList.toggle('dark-mode');
            
            if (body.classList.contains('dark-mode')) {
                localStorage.setItem('theme', 'dark');
                themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            } else {
                localStorage.setItem('theme', 'light');
                themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            }
        });
    } else {
        // Apply dark mode if saved, even if toggle button is not present
        if (localStorage.getItem('theme') === 'dark') {
            body.classList.add('dark-mode');
        }
    }
    
    // Handle checkout form submission
    const checkoutForm = document.getElementById('checkout-form');
    
    if (checkoutForm) {
        checkoutForm.addEventListener('submit', handleCheckoutSubmission);
    }
});

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.getElementById('notification');
    
    if (notification) {
        notification.textContent = message;
        notification.className = 'notification show';
        
        if (type === 'error') {
            notification.classList.add('error');
        } else if (type === 'info') {
            notification.classList.add('info');
        }
        
        setTimeout(() => {
            notification.classList.remove('show');
        }, 3000);
    } else {
        console.log('Notification:', message, type);
    }
}




