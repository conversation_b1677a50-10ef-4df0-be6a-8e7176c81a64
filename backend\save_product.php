<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Validate form data
if (!isset($_POST['name']) || !isset($_POST['category']) || !isset($_POST['price']) || !isset($_POST['stock'])) {
    json_response(['success' => false, 'message' => 'Missing required fields']);
}

$id = isset($_POST['id']) ? intval($_POST['id']) : 0;
$name = sanitize($_POST['name']);
$category = sanitize($_POST['category']);
$price = floatval($_POST['price']);
$stock = intval($_POST['stock']);
$description = isset($_POST['description']) ? sanitize($_POST['description']) : '';

// Validate data
if (empty($name) || empty($category) || $price < 0 || $stock < 0) {
    json_response(['success' => false, 'message' => 'Invalid data']);
}

try {
    // Start transaction
    $db->beginTransaction();
    
    // Handle image upload
    $imagePath = 'uploads/default-product.jpg';
    
    if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
        // Create uploads directory if it doesn't exist
        $uploadDir = '../uploads/';
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        $fileName = time() . '_' . basename($_FILES['image']['name']);
        $uploadFile = $uploadDir . $fileName;
        
        // Check if file is an image
        $check = getimagesize($_FILES['image']['tmp_name']);
        if ($check === false) {
            json_response(['success' => false, 'message' => 'File is not an image']);
        }
        
        // Move uploaded file
        if (move_uploaded_file($_FILES['image']['tmp_name'], $uploadFile)) {
            $imagePath = 'uploads/' . $fileName;
        } else {
            $error = error_get_last();
            json_response(['success' => false, 'message' => 'Failed to upload image: ' . ($error ? $error['message'] : 'Unknown error')]);
        }
    }
    
    if ($id > 0) {
        // Update existing product
        
        // Get current product data
        $stmt = $db->prepare("SELECT image FROM products WHERE id = :id");
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        $product = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$product) {
            $db->rollBack();
            json_response(['success' => false, 'message' => 'Product not found']);
        }
        
        // Keep current image if no new image uploaded
        if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
            $imagePath = $product['image'];
        }
        
        // Update product
        $stmt = $db->prepare("UPDATE products SET name = :name, category = :category, price = :price, stock = :stock, description = :description, image = :image WHERE id = :id");
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':category', $category);
        $stmt->bindParam(':price', $price);
        $stmt->bindParam(':stock', $stock);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':image', $imagePath);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        $message = 'Product updated successfully';
    } else {
        // Insert new product
        $stmt = $db->prepare("INSERT INTO products (name, category, price, stock, description, image) VALUES (:name, :category, :price, :stock, :description, :image)");
        $stmt->bindParam(':name', $name);
        $stmt->bindParam(':category', $category);
        $stmt->bindParam(':price', $price);
        $stmt->bindParam(':stock', $stock);
        $stmt->bindParam(':description', $description);
        $stmt->bindParam(':image', $imagePath);
        $stmt->execute();
        
        $id = $db->lastInsertId();
        $message = 'Product added successfully';
    }
    
    // Commit transaction
    $db->commit();
    
    json_response(['success' => true, 'message' => $message, 'id' => $id]);
} catch(PDOException $e) {
    // Rollback transaction on error
    $db->rollBack();
    json_response(['success' => false, 'message' => 'Error saving product: ' . $e->getMessage()]);
} catch(Exception $e) {
    // Rollback transaction on error
    $db->rollBack();
    json_response(['success' => false, 'message' => 'Error: ' . $e->getMessage()]);
}




