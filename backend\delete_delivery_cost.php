<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    json_response(['success' => false, 'message' => 'Invalid request method']);
}

// Get wilaya code
$wilaya_code = isset($_POST['wilaya_code']) ? sanitize($_POST['wilaya_code']) : '';

// Validate wilaya code
if (empty($wilaya_code)) {
    json_response(['success' => false, 'message' => 'Wilaya code is required']);
}

try {
    // Check if wilaya exists
    $stmt = $db->prepare("SELECT wilaya_name FROM delivery_costs WHERE wilaya_code = :wilaya_code");
    $stmt->bindParam(':wilaya_code', $wilaya_code);
    $stmt->execute();
    
    $wilaya = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$wilaya) {
        json_response(['success' => false, 'message' => 'Wilaya not found']);
    }
    
    // Delete the wilaya
    $stmt = $db->prepare("DELETE FROM delivery_costs WHERE wilaya_code = :wilaya_code");
    $stmt->bindParam(':wilaya_code', $wilaya_code);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        json_response(['success' => true, 'message' => 'Wilaya deleted successfully']);
    } else {
        json_response(['success' => false, 'message' => 'Failed to delete wilaya']);
    }
    
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error deleting wilaya: ' . $e->getMessage()]);
}
?>
