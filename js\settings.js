document.addEventListener('DOMContentLoaded', function() {
    // Load admin settings
    loadAdminSettings();
    
    // Settings form
    const settingsForm = document.getElementById('settings-form');
    
    if (settingsForm) {
        settingsForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Validate form
            const username = document.getElementById('username').value;
            const newPassword = document.getElementById('new-password').value;
            const confirmPassword = document.getElementById('confirm-password').value;
            
            if (!username) {
                showNotification('Username cannot be empty', 'error');
                return;
            }
            
            if (newPassword && newPassword !== confirmPassword) {
                showNotification('Passwords do not match', 'error');
                return;
            }
            
            // Save settings
            saveSettings();
        });
    }
});

// Load admin settings
function loadAdminSettings() {
    fetch('../backend/get_admin_settings.php')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('username').value = data.username;
            } else {
                showNotification(data.message || 'Failed to load settings', 'error');
            }
        })
        .catch(error => {
            console.error('Error loading settings:', error);
            showNotification('Failed to load settings', 'error');
        });
}

// Save settings
function saveSettings() {
    const formData = new FormData(document.getElementById('settings-form'));
    
    fetch('../backend/update_admin_settings.php', {
        method: 'POST',
        body: formData
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message || 'Settings updated successfully');
                
                // Clear password fields
                document.getElementById('current-password').value = '';
                document.getElementById('new-password').value = '';
                document.getElementById('confirm-password').value = '';
            } else {
                showNotification(data.message || 'Failed to update settings', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating settings:', error);
            showNotification('Failed to update settings', 'error');
        });
}





