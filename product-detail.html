<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Details - E-Commerce</title>
    <link rel="stylesheet" href="css/style.css?v=2.1">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="shortcut icon" href="ecommerce_11264152.png" type="image/x-icon">
    <style>
        /* Inline notification styles to ensure they work */
        .cart-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            background-color: #4CAF50;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 10000;
            opacity: 0;
            transform: translateY(-20px);
            transition: opacity 0.3s, transform 0.3s;
            font-weight: 500;
        }
        
        .cart-notification.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .cart-notification.info {
            background-color: #2196F3;
        }
    </style>
</head>
<body>
    <!-- Header with improved logo -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <i class="fas fa-shopping-bag logo-icon"></i>
                    <span class="logo-text">Finix</span>
                </a>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="index.html">Home</a></li>
                    <li><a href="products.html" class="active">Products</a></li>
                    <li><a href="about.html">About</a></li>
                    <li><a href="contact.html">Contact</a></li>
                </ul>
            </nav>
            <div class="nav-icons">
                <button id="theme-toggle" class="theme-toggle">
                    <i class="fas fa-moon"></i>
                </button>
                <a href="#" id="cart-icon" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count" id="cart-count">0</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Product Loading -->
    <div id="product-loading" class="loading-container">
        <div class="loading">Loading product details...</div>
    </div>

    <!-- Product Not Found Message -->
    <div id="product-not-found" class="product-not-found" style="display: none;">
        Product not found
    </div>

    <!-- Product Details -->
    <section id="product-details" class="product-details-section" style="display: none;">
        <div class="container">
            <!-- Breadcrumb Navigation -->
            <nav class="breadcrumb">
                <a href="index.html"><i class="fas fa-home"></i> Home</a>
                <span class="breadcrumb-separator">/</span>
                <span id="product-category">Products</span>
                <span class="breadcrumb-separator">/</span>
                <span id="product-breadcrumb-name">Product Details</span>
            </nav>

            <div class="product-details-container">
                <!-- Product Image Section -->
                <div class="product-image-section">
                    <div class="product-image-main">
                        <img id="product-image" src="" alt="Product Image">
                        <div class="image-zoom-hint">
                            <i class="fas fa-search-plus"></i>
                            <span>Click to zoom</span>
                        </div>
                    </div>
                </div>

                <!-- Product Information Section -->
                <div class="product-info-section">
                    <div class="product-header">
                        <h1 id="product-name">Product Name</h1>
                        <div class="product-meta">
                            <span class="product-category-tag" id="product-category-tag">Category</span>
                            <span class="product-stock" id="product-stock">In Stock</span>
                        </div>
                    </div>

                    <div class="product-pricing">
                        <div class="price-main" id="product-price">0.00 DZD</div>
                        <div class="price-note">
                            <i class="fas fa-info-circle"></i>
                            <span>Price includes all taxes</span>
                        </div>
                    </div>

                    <div class="product-description-section">
                        <h3><i class="fas fa-align-left"></i> Description</h3>
                        <div class="product-description" id="product-description">
                            Product description will be displayed here.
                        </div>
                    </div>

                    <div class="product-actions">
                        <div class="quantity-selector">
                            <label for="quantity">Quantity:</label>
                            <div class="quantity-controls">
                                <button type="button" class="qty-btn" onclick="decreaseQuantity()">-</button>
                                <input type="number" id="quantity" value="1" min="1" max="10">
                                <button type="button" class="qty-btn" onclick="increaseQuantity()">+</button>
                            </div>
                        </div>

                        <div class="action-buttons">
                            <button id="add-to-cart-btn" class="btn-primary">
                                <i class="fas fa-shopping-cart"></i>
                                <span>Add to Cart</span>
                            </button>
                            <button class="btn-secondary" onclick="goBack()">
                                <i class="fas fa-arrow-left"></i>
                                <span>Back to Products</span>
                            </button>
                        </div>
                    </div>

                    <!-- Product Features -->
                    <div class="product-features">
                        <h3><i class="fas fa-star"></i> Features</h3>
                        <div class="features-grid">
                            <div class="feature-item">
                                <i class="fas fa-shipping-fast"></i>
                                <div>
                                    <strong>Fast Delivery</strong>
                                    <span>Delivery across Algeria</span>
                                </div>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-shield-alt"></i>
                                <div>
                                    <strong>Quality Guarantee</strong>
                                    <span>100% authentic products</span>
                                </div>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-headset"></i>
                                <div>
                                    <strong>Customer Support</strong>
                                    <span>24/7 assistance</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="js/main.js"></script>
    <script src="js/cart.js"></script>
    <script>
        // Inline script to ensure notification functionality works
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Product detail page loaded');
            
            // Get product ID from URL
            const urlParams = new URLSearchParams(window.location.search);
            const productId = urlParams.get('id');
            
            if (productId) {
                // Load product details
                loadProductDetails(productId);
            } else {
                // Show error message
                document.getElementById('product-loading').style.display = 'none';
                document.getElementById('product-not-found').style.display = 'block';
            }
            
            // Update cart count
            updateCartCount();
        });

        // Load product details
        function loadProductDetails(productId) {
            console.log('Loading product details for ID:', productId);
            
            // Show loading
            document.getElementById('product-loading').style.display = 'flex';
            document.getElementById('product-details').style.display = 'none';
            
            // Fetch product details
            fetch(`backend/get_product_public.php?id=${productId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(product => {
                    console.log('Product loaded:', product);
                    
                    if (!product) {
                        throw new Error('Product not found');
                    }
                    
                    // Update product details
                    document.getElementById('product-name').textContent = product.name;
                    document.getElementById('product-price').textContent = product.price + ' DZD';
                    document.getElementById('product-description').textContent = product.description || 'No description available';
                    document.getElementById('product-image').src = product.image;
                    
                    // Add click event listener to Add to Cart button
                    const addToCartBtn = document.getElementById('add-to-cart-btn');
                    addToCartBtn.onclick = function() {
                        addToCartWithQuantity();
                    };
                    
                    // Hide loading, show product details
                    document.getElementById('product-loading').style.display = 'none';
                    document.getElementById('product-details').style.display = 'block';
                })
                .catch(error => {
                    console.error('Error loading product:', error);
                    document.getElementById('product-loading').style.display = 'none';
                    document.getElementById('product-not-found').style.display = 'block';
                });
        }

        // Add product to cart with notification
        function addProductToCart(productId, productName, productPrice, productImage) {
            console.log('Adding product to cart:', productId, productName, productPrice, productImage);
            
            // Get current cart
            let cart = JSON.parse(localStorage.getItem('cart')) || [];
            
            // Check if product already in cart
            const existingProductIndex = cart.findIndex(item => item.id === productId);
            
            if (existingProductIndex !== -1) {
                // Product already in cart
                showCartNotification('Product already in cart', 'info');
            } else {
                // Add product to cart
                cart.push({
                    id: productId,
                    name: productName,
                    price: productPrice,
                    image: productImage,
                    quantity: 1
                });
                
                // Save cart
                localStorage.setItem('cart', JSON.stringify(cart));
                
                // Show notification
                showCartNotification('Product added to cart');
                
                // Update cart count
                updateCartCount();
            }
        }

        // Show cart notification
        function showCartNotification(message, type = 'success') {
            // Remove any existing notifications
            const existingNotifications = document.querySelectorAll('.cart-notification');
            existingNotifications.forEach(notification => {
                document.body.removeChild(notification);
            });
            
            // Create notification
            const notification = document.createElement('div');
            notification.className = `cart-notification ${type}`;
            notification.textContent = message;
            
            // Add to body
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);
            
            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                
                // Remove from DOM after animation
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }

        // Update cart count
        function updateCartCount() {
            const cart = JSON.parse(localStorage.getItem('cart')) || [];
            const cartCount = document.getElementById('cart-count');

            if (cartCount) {
                cartCount.textContent = cart.length;
            }
        }

        // Quantity control functions
        function increaseQuantity() {
            const quantityInput = document.getElementById('quantity');
            const currentValue = parseInt(quantityInput.value) || 1;
            const maxValue = parseInt(quantityInput.max) || 10;

            if (currentValue < maxValue) {
                quantityInput.value = currentValue + 1;
            }
        }

        function decreaseQuantity() {
            const quantityInput = document.getElementById('quantity');
            const currentValue = parseInt(quantityInput.value) || 1;
            const minValue = parseInt(quantityInput.min) || 1;

            if (currentValue > minValue) {
                quantityInput.value = currentValue - 1;
            }
        }

        // Back button functionality
        function goBack() {
            if (document.referrer && document.referrer.includes(window.location.hostname)) {
                window.history.back();
            } else {
                window.location.href = 'index.html';
            }
        }

        // Enhanced add to cart with quantity
        function addToCartWithQuantity() {
            const productId = getProductIdFromUrl();
            const productName = document.getElementById('product-name').textContent;
            const productPrice = parseFloat(document.getElementById('product-price').textContent.replace(' DZD', ''));
            const productImage = document.getElementById('product-image').src;
            const quantity = parseInt(document.getElementById('quantity').value) || 1;

            // Add multiple items if quantity > 1
            for (let i = 0; i < quantity; i++) {
                addProductToCart(productId, productName, productPrice, productImage);
            }

            // Reset quantity to 1 after adding
            document.getElementById('quantity').value = 1;
        }

        // Image zoom functionality
        document.addEventListener('DOMContentLoaded', function() {
            const productImage = document.getElementById('product-image');
            if (productImage) {
                productImage.addEventListener('click', function() {
                    // Simple zoom effect
                    if (this.style.transform === 'scale(1.5)') {
                        this.style.transform = 'scale(1)';
                        this.style.cursor = 'zoom-in';
                        this.style.position = 'relative';
                        this.style.zIndex = '1';
                    } else {
                        this.style.transform = 'scale(1.5)';
                        this.style.cursor = 'zoom-out';
                        this.style.position = 'relative';
                        this.style.zIndex = '10';
                        this.style.transition = 'transform 0.3s ease';
                    }
                });
            }
        });
    </script>
</body>
</html>

