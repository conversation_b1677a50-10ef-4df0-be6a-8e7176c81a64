<?php
require_once 'config.php';

// Check admin login
check_admin_login();

try {
    // Get total orders
    $stmt = $db->query("SELECT COUNT(*) FROM orders");
    $totalOrders = $stmt->fetchColumn();
    
    // Get total products
    $stmt = $db->query("SELECT COUNT(*) FROM products");
    $totalProducts = $stmt->fetchColumn();
    
    // Get pending orders
    $stmt = $db->prepare("SELECT COUNT(*) FROM orders WHERE status = :status");
    $status = 'Pending';
    $stmt->bindParam(':status', $status);
    $stmt->execute();
    $pendingOrders = $stmt->fetchColumn();
    
    // Get total revenue (only from Completed orders)
    $stmt = $db->query("SELECT SUM(total_amount) FROM orders WHERE status = 'Completed'");
    $totalRevenue = $stmt->fetchColumn() ?: 0;
    
    // Return stats
    json_response([
        'totalOrders' => $totalOrders,
        'totalProducts' => $totalProducts,
        'pendingOrders' => $pendingOrders,
        'totalRevenue' => number_format($totalRevenue, 2)
    ]);
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error fetching dashboard stats: ' . $e->getMessage()]);
}

