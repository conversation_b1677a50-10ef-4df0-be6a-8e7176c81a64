// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Product detail page loaded');
    
    // Get product ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const productId = urlParams.get('id');
    
    if (productId) {
        // Load product details
        loadProductDetails(productId);
    } else {
        // Show error message
        document.getElementById('product-loading').style.display = 'none';
        document.getElementById('product-not-found').style.display = 'block';
    }
    
    // Add event listener to Add to Cart button
    document.getElementById('add-to-cart-btn').addEventListener('click', function() {
        const productId = this.getAttribute('data-id');
        const productName = this.getAttribute('data-name');
        const productPrice = parseFloat(this.getAttribute('data-price'));
        const productImage = this.getAttribute('data-image');
        
        addProductToCart(productId, productName, productPrice, productImage);
    });
});

// Load product details
function loadProductDetails(productId) {
    console.log('Loading product details for ID:', productId);
    
    // Show loading
    document.getElementById('product-loading').style.display = 'flex';
    document.getElementById('product-details').style.display = 'none';
    
    // Fetch product details
    fetch(`backend/get_product.php?id=${productId}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(product => {
            console.log('Product loaded:', product);
            
            if (!product) {
                throw new Error('Product not found');
            }
            
            // Update product details
            document.getElementById('product-name').textContent = product.name;
            document.getElementById('product-price').textContent = product.price + ' DZD';
            document.getElementById('product-description').textContent = product.description || 'No description available';
            document.getElementById('product-image').src = product.image;
            
            // Set data attributes for Add to Cart button
            const addToCartBtn = document.getElementById('add-to-cart-btn');
            addToCartBtn.setAttribute('data-id', product.id);
            addToCartBtn.setAttribute('data-name', product.name);
            addToCartBtn.setAttribute('data-price', product.price);
            addToCartBtn.setAttribute('data-image', product.image);
            
            // Hide loading, show product details
            document.getElementById('product-loading').style.display = 'none';
            document.getElementById('product-details').style.display = 'block';
        })
        .catch(error => {
            console.error('Error loading product:', error);
            document.getElementById('product-loading').style.display = 'none';
            document.getElementById('product-not-found').style.display = 'block';
        });
}

// Add product to cart
function addProductToCart(productId, productName, productPrice, productImage) {
    console.log('Adding product to cart:', productId, productName, productPrice, productImage);
    
    // Get current cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Check if product already in cart
    const existingProductIndex = cart.findIndex(item => item.id === parseInt(productId));
    
    if (existingProductIndex !== -1) {
        // Product already in cart, show notification
        showSimpleNotification('Product already in cart', 'info');
    } else {
        // Add product to cart
        cart.push({
            id: parseInt(productId),
            name: productName,
            price: productPrice,
            image: productImage,
            quantity: 1
        });
        
        // Save cart
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // Show success notification
        showSimpleNotification('Product added to cart', 'success');
        
        // Update cart count
        if (typeof updateCartCount === 'function') {
            updateCartCount();
        }
    }
}

// Simple notification function
function showSimpleNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to body
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        
        // Remove from DOM after animation
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

