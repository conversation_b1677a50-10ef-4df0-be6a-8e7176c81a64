<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Get request body
$data = json_decode(file_get_contents('php://input'), true);

// Validate data
if (!isset($data['id']) || !isset($data['status'])) {
    json_response(['success' => false, 'message' => 'Missing required fields']);
}

$id = intval($data['id']);
$status = sanitize($data['status']);

// Validate status
$validStatuses = ['Pending', 'Processing', 'Completed', 'Cancelled'];
if (!in_array($status, $validStatuses)) {
    json_response(['success' => false, 'message' => 'Invalid status']);
}

try {
    // Check if order exists
    $stmt = $db->prepare("SELECT id FROM orders WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    
    if (!$stmt->fetch()) {
        json_response(['success' => false, 'message' => 'Order not found']);
    }
    
    // Update order status
    $stmt = $db->prepare("UPDATE orders SET status = :status WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->bindParam(':status', $status);
    $stmt->execute();
    
    // Try to log the status change, but don't fail if table doesn't exist
    try {
        $adminId = $_SESSION['admin_id'];
        $stmt = $db->prepare("INSERT INTO order_history (order_id, admin_id, status, change_date) VALUES (:order_id, :admin_id, :status, NOW())");
        $stmt->bindParam(':order_id', $id);
        $stmt->bindParam(':admin_id', $adminId);
        $stmt->bindParam(':status', $status);
        $stmt->execute();
    } catch(PDOException $e) {
        // Just log the error but continue - don't let this stop the status update
        error_log('Could not log order history: ' . $e->getMessage());
    }
    
    json_response(['success' => true, 'message' => 'Order status updated successfully']);
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error updating order status: ' . $e->getMessage()]);
}





