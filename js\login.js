document.addEventListener('DOMContentLoaded', function() {
    // Login form
    const loginForm = document.getElementById('login-form');
    
    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            // Validate form
            if (!username || !password) {
                showNotification('Please fill in all fields', 'error');
                return;
            }
            
            // Create form data
            const formData = new FormData();
            formData.append('username', username);
            formData.append('password', password);
            
            // Submit login
            fetch('backend/admin_login.php', {
                method: 'POST',
                body: formData
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(data.message || 'Login successful');
                        
                        // Redirect to dashboard
                        setTimeout(() => {
                            window.location.href = 'admin/dashboard.html';
                        }, 1000);
                    } else {
                        showNotification(data.message || 'Login failed', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error logging in:', error);
                    showNotification('Login failed', 'error');
                });
        });
    }
    
    // Theme toggle for login page (uses CLIENT theme - SEPARATE from admin)
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;

    // Migration: Move old unified 'theme' to 'client-theme' for separation
    if (localStorage.getItem('theme') && !localStorage.getItem('client-theme')) {
        localStorage.setItem('client-theme', localStorage.getItem('theme'));
        localStorage.removeItem('theme'); // Remove unified theme to force separation
    }

    // Check for saved CLIENT theme preference (login page uses client theme)
    if (localStorage.getItem('client-theme') === 'dark') {
        body.classList.add('dark-mode');
        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
    }

    themeToggle.addEventListener('click', function() {
        body.classList.toggle('dark-mode');

        if (body.classList.contains('dark-mode')) {
            localStorage.setItem('client-theme', 'dark');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
        } else {
            localStorage.setItem('client-theme', 'light');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
        }
    });
});

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        
        // Remove notification from DOM after animation
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}