<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Debug - Finix DZ</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .debug-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 600px;
            margin: 0 auto;
        }
        .storage-item {
            padding: 10px;
            margin: 5px 0;
            background: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .storage-item.client {
            border-left-color: #28a745;
        }
        .storage-item.admin {
            border-left-color: #dc3545;
        }
        .storage-item.old {
            border-left-color: #ffc107;
        }
        button {
            padding: 10px 15px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-client { background: #28a745; color: white; }
        .btn-admin { background: #dc3545; color: white; }
        .btn-clear { background: #6c757d; color: white; }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>Theme Debug Tool</h1>
        <p>This page shows you exactly what's stored in localStorage for themes.</p>
        
        <h3>Current localStorage Values:</h3>
        <div id="storage-display"></div>
        
        <h3>Test Theme Changes:</h3>
        <button class="btn-client" onclick="setClientTheme('dark')">Set Client Dark</button>
        <button class="btn-client" onclick="setClientTheme('light')">Set Client Light</button>
        <button class="btn-admin" onclick="setAdminTheme('dark')">Set Admin Dark</button>
        <button class="btn-admin" onclick="setAdminTheme('light')">Set Admin Light</button>
        <button class="btn-clear" onclick="clearAllThemes()">Clear All Themes</button>
        <button class="btn-clear" onclick="refreshDisplay()">Refresh Display</button>
        
        <h3>Navigation:</h3>
        <a href="index.html">Go to Client Pages</a> | 
        <a href="login.html">Go to Admin Login</a>
        
        <h3>Explanation:</h3>
        <p><strong>The Problem:</strong> localStorage is shared across the entire domain (localhost in your case). 
        So both client and admin pages can see and modify the same storage.</p>
        
        <p><strong>In Production:</strong> This is actually the intended behavior! The same user on the same device 
        should have consistent theme preferences across your entire website.</p>
        
        <p><strong>True Separation:</strong> Would require different domains/subdomains:
        <br>• Client: shop.yoursite.com
        <br>• Admin: admin.yoursite.com</p>
    </div>

    <script>
        function refreshDisplay() {
            const display = document.getElementById('storage-display');
            display.innerHTML = '';
            
            // Check all possible theme keys
            const keys = ['theme', 'client-theme', 'admin-theme'];
            let hasAny = false;
            
            keys.forEach(key => {
                const value = localStorage.getItem(key);
                if (value) {
                    hasAny = true;
                    const div = document.createElement('div');
                    div.className = 'storage-item ' + getKeyClass(key);
                    div.innerHTML = `<strong>${key}:</strong> ${value}`;
                    display.appendChild(div);
                }
            });
            
            if (!hasAny) {
                display.innerHTML = '<div class="storage-item">No theme preferences found in localStorage</div>';
            }
        }
        
        function getKeyClass(key) {
            if (key === 'client-theme') return 'client';
            if (key === 'admin-theme') return 'admin';
            return 'old';
        }
        
        function setClientTheme(theme) {
            localStorage.setItem('client-theme', theme);
            refreshDisplay();
        }
        
        function setAdminTheme(theme) {
            localStorage.setItem('admin-theme', theme);
            refreshDisplay();
        }
        
        function clearAllThemes() {
            localStorage.removeItem('theme');
            localStorage.removeItem('client-theme');
            localStorage.removeItem('admin-theme');
            refreshDisplay();
        }
        
        // Auto-refresh display every 2 seconds
        setInterval(refreshDisplay, 2000);
        
        // Initial display
        refreshDisplay();
    </script>
</body>
</html>
