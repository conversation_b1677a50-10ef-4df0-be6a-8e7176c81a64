document.addEventListener('DOMContentLoaded', function() {
    // Load products
    loadProducts();
    
    // Add product button
    const addProductBtn = document.getElementById('add-product-btn');
    
    if (addProductBtn) {
        addProductBtn.addEventListener('click', function() {
            showProductForm();
        });
    }
    
    // Product form
    const productForm = document.getElementById('product-form');
    
    if (productForm) {
        productForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveProduct();
        });
    }
});

// Load products
function loadProducts() {
    fetch('../backend/get_products.php')
        .then(response => response.json())
        .then(products => {
            const tableBody = document.getElementById('products-body');
            
            if (!tableBody) return;
            
            if (products.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="6" class="text-center">No products found</td></tr>';
                return;
            }
            
            let html = '';
            
            products.forEach(product => {
                html += `
                    <tr>
                        <td><img src="../${product.image}" alt="${product.name}" class="product-thumbnail"></td>
                        <td>${product.name}</td>
                        <td>${product.category}</td>
                        <td>${product.price} DZD</td>
                        <td>${product.stock}</td>
                        <td>
                            <button class="admin-btn admin-btn-primary btn-sm" onclick="editProduct(${product.id})">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="admin-btn admin-btn-danger btn-sm" onclick="deleteProduct(${product.id})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading products:', error);
            showNotification('Failed to load products', 'error');
        });
}

// Handle image preview
document.addEventListener('DOMContentLoaded', function() {
    const imageInput = document.getElementById('product-image');
    const imagePreview = document.getElementById('product-image-preview');
    
    if (imageInput && imagePreview) {
        imageInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    // Clear previous preview
                    imagePreview.innerHTML = '';
                    
                    // Create image element
                    const img = document.createElement('img');
                    img.src = e.target.result;
                    
                    // Add image to preview
                    imagePreview.appendChild(img);
                }
                
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
    
    // Show current product image when editing
    function showProductForm(id = null) {
        const formContainer = document.getElementById('product-form-container');
        const formTitle = document.getElementById('product-form-title');
        const form = document.getElementById('product-form');
        
        // Reset form
        form.reset();
        document.getElementById('product-id').value = '';
        
        if (id) {
            // Edit product
            formTitle.textContent = 'Edit Product';
            
            // Load product data
            fetch(`../backend/get_product.php?id=${id}`)
                .then(response => response.json())
                .then(product => {
                    document.getElementById('product-id').value = product.id;
                    document.getElementById('product-name').value = product.name;
                    document.getElementById('product-category').value = product.category;
                    document.getElementById('product-price').value = product.price;
                    document.getElementById('product-stock').value = product.stock;
                    document.getElementById('product-description').value = product.description || '';
                    
                    // Show product image
                    if (product.image) {
                        imagePreview.innerHTML = `<img src="../${product.image}" alt="${product.name}">`;
                    } else {
                        imagePreview.innerHTML = '';
                    }
                    
                    // Show form
                    formContainer.style.display = 'block';
                })
                .catch(error => {
                    console.error('Error loading product:', error);
                    showNotification('Failed to load product', 'error');
                });
        } else {
            // Add new product
            formTitle.textContent = 'Add New Product';
            
            // Clear image preview
            imagePreview.innerHTML = '';
            
            // Show form
            formContainer.style.display = 'block';
        }
    }
    
    // Replace the existing showProductForm function
    window.showProductForm = showProductForm;
});

// Save product
function saveProduct() {
    const form = document.getElementById('product-form');
    const formData = new FormData(form);
    
    // Show loading indicator
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalBtnText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
    submitBtn.disabled = true;
    
    fetch('../backend/save_product.php', {
        method: 'POST',
        body: formData
    })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Reset button
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
            
            if (data.success) {
                showNotification(data.message || 'Product saved successfully');
                
                // Hide form
                document.getElementById('product-form-container').style.display = 'none';
                
                // Reload products
                loadProducts();
            } else {
                showNotification(data.message || 'Failed to save product', 'error');
                console.error('Error details:', data);
            }
        })
        .catch(error => {
            // Reset button
            submitBtn.innerHTML = originalBtnText;
            submitBtn.disabled = false;
            
            console.error('Error saving product:', error);
            showNotification('Failed to save product: ' + error.message, 'error');
        });
}

// Edit product
function editProduct(id) {
    showProductForm(id);
}

// Delete product
function deleteProduct(id) {
    if (confirm('Are you sure you want to delete this product?')) {
        fetch(`../backend/delete_product.php?id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message || 'Product deleted successfully');
                    
                    // Reload products
                    loadProducts();
                } else {
                    showNotification(data.message || 'Failed to delete product', 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting product:', error);
                showNotification('Failed to delete product', 'error');
            });
    }
}





