<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finix DZ - Algerian E-Commerce</title>
    <link rel="stylesheet" href="css/style.css?v=2.4">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.3/css/all.min.css">
    <link rel="shortcut icon" href="ecommerce_11264152.png" type="image/x-icon">
    <style>
        /* Inline notification styles to ensure they work */
        .cart-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 25px;
            border-radius: 8px;
            background-color: #4CAF50;
            color: white;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
            z-index: 10000;
            opacity: 0;
            transform: translateY(-20px);
            transition: opacity 0.3s, transform 0.3s;
            font-weight: 500;
        }
        
        .cart-notification.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .cart-notification.info {
            background-color: #2196F3;
        }
    </style>
</head>
<body>
    <!-- Header with improved logo -->
    <header class="header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <i class="fas fa-shopping-bag logo-icon"></i>
                    <span class="logo-text">Finix</span>
                </a>
            </div>
            <nav class="nav">
                <ul>
                    <li><a href="index.html" class="active">Home</a></li>
                    <li><a href="#products">Products</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </nav>
            <div class="nav-icons">
                <button id="theme-toggle" class="theme-toggle">
                    <i class="fas fa-moon"></i>
                </button>
                <a href="#" id="cart-icon" class="cart-icon">
                    <i class="fas fa-shopping-cart"></i>
                    <span class="cart-count" id="cart-count">0</span>
                </a>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <h2>Quality Products for Every Need</h2>
                <p>Discover premium electronics, clothing, home decor and more, delivered to your doorstep in Algeria.</p>
                <a href="#products" class="btn btn-primary">Shop Now</a>
            </div>
        </div>
    </section>

    <!-- Products Section -->
    <section id="products" class="products-section">
        <div class="container">
            <h2 class="section-title">Our Products</h2>
            <div class="filter-container">
                <button class="filter-btn active" data-filter="all">All Products</button>
                <button class="filter-btn" data-filter="electronics">Electronics</button>
                <button class="filter-btn" data-filter="clothing">Clothing</button>
                <button class="filter-btn" data-filter="home">Home & Decor</button>
                <button class="filter-btn" data-filter="accessories">Accessories</button>
                <button class="filter-btn" data-filter="others">Others</button>
            </div>
            <div class="products-grid">
                <!-- Product cards will be loaded dynamically -->
            </div>
        </div>
    </section>

    <!-- About Section -->
    <section id="about" class="about-section">
        <div class="container">
            <h2 class="section-title">About Us</h2>
            <div class="about-content">
                <div class="about-image">
                    <img src="logo.png" alt="Finix DZ Team">
                </div>
                <div class="about-text">
                    <p>Finix DZ is a leading retailer serving customers across Algeria. Founded in 2025, we've quickly become a trusted source for high-quality electronics, clothing, home decor, and accessories.</p>
                    <p>Our mission is to make premium products accessible to everyone in Algeria through our convenient online shopping experience and reliable nationwide delivery.</p>
                    <p>We carefully select each product in our catalog to ensure quality, authenticity, and value for our customers. Our team of experts tests every product before it reaches our shelves.</p>
                    <p>At Finix DZ, we believe that everyone deserves access to quality products at fair prices. That's why we offer a wide range of items to suit all preferences and budgets.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
        <div class="container">
            <h2 class="section-title">Contact Us</h2>
            <div class="contact-content">
                <div class="contact-info">
                    <div class="contact-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <h3>Our Location</h3>
                            <p>123 Beauty Street, Algiers, Algeria</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-phone"></i>
                        <div>
                            <h3>Phone Number</h3>
                            <p>+213 123 456 789</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div>
                            <h3>Email Address</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-clock"></i>
                        <div>
                            <h3>Working Hours</h3>
                            <p>Sunday - Thursday: 9am - 5pm</p>
                            <p>Friday - Saturday: Closed</p>
                        </div>
                    </div>
                </div>
                <div class="contact-map">
                   <a href="https://www.google.com/maps/"> <img src="map.jpeg" alt="Our Location on Map"> </a>
                    <p class="map-caption">Find us in the heart of Algiers</p>
                </div>
            </div>
        </div>
    </section>

    <footer style="background-color: #2c3e50; color: #ffffff;">
        <div class="container">
            <div class="footer-content">
                <div class="footer-logo">
                    <h2 style="color: #3498db;">Finix DZ</h2>
                    <p style="color: #ffffff;">Your one-stop shop in Algeria. We bring the best products from around the world to your doorstep.</p>
                </div>
                <div class="footer-links">
                    <h3 style="color: #3498db;">Quick Links</h3>
                    <ul>
                        <li><a href="index.html" style="color: #ffffff;">Home</a></li>
                        <li><a href="#products" style="color: #ffffff;">Products</a></li>
                        <li><a href="#about" style="color: #ffffff;">About</a></li>
                        <li><a href="#contact" style="color: #ffffff;">Contact</a></li>
                        <li><a href="checkout.html" style="color: #ffffff;">Checkout</a></li>
                    </ul>
                </div>
                <div class="footer-contact">
                    <h3 style="color: #3498db;">Contact Us</h3>
                    <p style="color: #ffffff;"><i class="fas fa-map-marker-alt" style="color: #3498db;"></i> 123 Commerce Street, Algiers, Algeria</p>
                    <p style="color: #ffffff;"><i class="fas fa-phone" style="color: #3498db;"></i> +213 123 456 789</p>
                    <p style="color: #ffffff;"><i class="fas fa-envelope" style="color: #3498db;"></i> <EMAIL></p>
                </div>
                <div class="footer-social">
                    <h3 style="color: #3498db;">Follow Us</h3>
                    <div class="social-icons">
                        <a href="#" style="color: #ffffff;"><i class="fab fa-facebook-f"></i></a>
                        <a href="#" style="color: #ffffff;"><i class="fab fa-instagram"></i></a>
                        <a href="#" style="color: #ffffff;"><i class="fab fa-twitter"></i></a>
                        <a href="#" style="color: #ffffff;"><i class="fab fa-tiktok"></i></a>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <p style="color: #ffffff;">&copy; 2025 Finix DZ. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js?v=2.2"></script>
    <script src="js/cart.js?v=2.2"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Setup product filtering
            setupProductFiltering();
            
            // Update cart count
            updateCartCount();
        });
        
        // Add to cart function with notification
        function addToCart(productId, productName, productPrice, productImage) {
            console.log('Adding product to cart:', productId, productName, productPrice, productImage);
            
            // Get current cart
            let cart = JSON.parse(localStorage.getItem('cart')) || [];
            
            // Check if product already in cart
            const existingProductIndex = cart.findIndex(item => item.id === productId);
            
            if (existingProductIndex !== -1) {
                // Product already in cart
                showCartNotification('Product already in cart', 'info');
            } else {
                // Add product to cart
                cart.push({
                    id: productId,
                    name: productName,
                    price: productPrice,
                    image: productImage,
                    quantity: 1
                });
                
                // Save cart
                localStorage.setItem('cart', JSON.stringify(cart));
                
                // Show notification
                showCartNotification('Product added to cart');
                
                // Update cart count
                updateCartCount();
            }
        }
        
        // Show cart notification
        function showCartNotification(message, type = 'success') {
            // Remove any existing notifications
            const existingNotifications = document.querySelectorAll('.cart-notification');
            existingNotifications.forEach(notification => {
                if (document.body.contains(notification)) {
                    document.body.removeChild(notification);
                }
            });
            
            // Create notification
            const notification = document.createElement('div');
            notification.className = `cart-notification ${type}`;
            notification.textContent = message;
            
            // Add to body
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 10);
            
            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                
                // Remove from DOM after animation
                setTimeout(() => {
                    if (document.body.contains(notification)) {
                        document.body.removeChild(notification);
                    }
                }, 300);
            }, 3000);
        }
    </script>
</body>
</html>


