<?php
require_once 'config.php';

// Check admin login
check_admin_login();

try {
    // Get delivery costs from database ordered by wilaya code numerically
    $stmt = $db->prepare("SELECT * FROM delivery_costs ORDER BY CAST(wilaya_code AS UNSIGNED) ASC");
    $stmt->execute();
    
    $deliveryCosts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // If no delivery costs found, initialize with default values
    if (empty($deliveryCosts)) {
        // Map of wilaya codes to names and default costs
        $wilayaData = [
            '1' => ['name' => 'ADRAR', 'domicile' => 1350, 'stopdesk' => 1070],
            '2' => ['name' => 'CHLEF', 'domicile' => 850, 'stopdesk' => 570],
            '3' => ['name' => 'LAGHOUAT', 'domicile' => 950, 'stopdesk' => 670],
            '4' => ['name' => 'OUM EL BOUAGHI', 'domicile' => 800, 'stopdesk' => 570],
            '5' => ['name' => 'BATNA', 'domicile' => 800, 'stopdesk' => 570],
            '6' => ['name' => 'BEJAIA', 'domicile' => 800, 'stopdesk' => 570],
            '7' => ['name' => 'BISKRA', 'domicile' => 950, 'stopdesk' => 670],
            '8' => ['name' => 'BECHAR', 'domicile' => 1300, 'stopdesk' => 870],
            '9' => ['name' => 'BLIDA', 'domicile' => 800, 'stopdesk' => 570],
            '10' => ['name' => 'BOUIRA', 'domicile' => 750, 'stopdesk' => 570],
            '11' => ['name' => 'TAMANRASSET', 'domicile' => 1600, 'stopdesk' => 1270],
            '12' => ['name' => 'TEBESSA', 'domicile' => 900, 'stopdesk' => 570],
            '13' => ['name' => 'TLEMCEN', 'domicile' => 900, 'stopdesk' => 570],
            '14' => ['name' => 'TIARET', 'domicile' => 850, 'stopdesk' => 570],
            '15' => ['name' => 'TIZI OUZOU', 'domicile' => 850, 'stopdesk' => 570],
            '16' => ['name' => 'ALGER', 'domicile' => 650, 'stopdesk' => 470],
            '17' => ['name' => 'DJELFA', 'domicile' => 950, 'stopdesk' => 670],
            '18' => ['name' => 'JIJEL', 'domicile' => 800, 'stopdesk' => 570],
            '19' => ['name' => 'SETIF', 'domicile' => 750, 'stopdesk' => 570],
            '20' => ['name' => 'SAIDA', 'domicile' => 850, 'stopdesk' => 570],
            '21' => ['name' => 'SKIKDA', 'domicile' => 850, 'stopdesk' => 570],
            '22' => ['name' => 'SIDI BEL ABBES', 'domicile' => 850, 'stopdesk' => 570],
            '23' => ['name' => 'ANNABA', 'domicile' => 850, 'stopdesk' => 570],
            '24' => ['name' => 'GUELMA', 'domicile' => 850, 'stopdesk' => 570],
            '25' => ['name' => 'CONSTANTINE', 'domicile' => 800, 'stopdesk' => 570],
            '26' => ['name' => 'MEDEA', 'domicile' => 800, 'stopdesk' => 570],
            '27' => ['name' => 'MOSTAGANEM', 'domicile' => 850, 'stopdesk' => 570],
            '28' => ['name' => 'M\'SILA', 'domicile' => 750, 'stopdesk' => 570],
            '29' => ['name' => 'MASCARA', 'domicile' => 850, 'stopdesk' => 570],
            '30' => ['name' => 'OUARGLA', 'domicile' => 1000, 'stopdesk' => 670],
            '31' => ['name' => 'ORAN', 'domicile' => 850, 'stopdesk' => 570],
            '32' => ['name' => 'EL BAYADH', 'domicile' => 950, 'stopdesk' => 670],
            '34' => ['name' => 'BORDJ BOU ARRERIDJ', 'domicile' => 800, 'stopdesk' => 570],
            '35' => ['name' => 'BOUMERDES', 'domicile' => 750, 'stopdesk' => 570],
            '36' => ['name' => 'EL TARF', 'domicile' => 900, 'stopdesk' => 570],
            '38' => ['name' => 'TISSEMSILT', 'domicile' => 850, 'stopdesk' => 570],
            '39' => ['name' => 'EL OUED', 'domicile' => 950, 'stopdesk' => 670],
            '40' => ['name' => 'KHENCHELA', 'domicile' => 850, 'stopdesk' => 570],
            '41' => ['name' => 'SOUK AHRAS', 'domicile' => 900, 'stopdesk' => 570],
            '42' => ['name' => 'TIPAZA', 'domicile' => 750, 'stopdesk' => 570],
            '43' => ['name' => 'MILA', 'domicile' => 800, 'stopdesk' => 570],
            '44' => ['name' => 'AIN DEFLA', 'domicile' => 800, 'stopdesk' => 570],
            '45' => ['name' => 'NAAMA', 'domicile' => 950, 'stopdesk' => 670],
            '46' => ['name' => 'AIN TEMOUCHENT', 'domicile' => 850, 'stopdesk' => 570],
            '47' => ['name' => 'GHARDAIA', 'domicile' => 1000, 'stopdesk' => 670],
            '48' => ['name' => 'RELIZANE', 'domicile' => 850, 'stopdesk' => 570],
            '49' => ['name' => 'TIMIMOUN', 'domicile' => 1350, 'stopdesk' => 1070],
            '51' => ['name' => 'OULED DJELLAL', 'domicile' => 950, 'stopdesk' => 670],
            '52' => ['name' => 'BENI ABBES', 'domicile' => 1300, 'stopdesk' => 870],
            '53' => ['name' => 'IN SALAH', 'domicile' => 1600, 'stopdesk' => 1270],
            '54' => ['name' => 'IN GUEZZAM', 'domicile' => 1600, 'stopdesk' => 1270],
            '55' => ['name' => 'TOUGGOURT', 'domicile' => 1000, 'stopdesk' => 670],
            '57' => ['name' => 'M\'GHAIR', 'domicile' => 950, 'stopdesk' => 670],
            '58' => ['name' => 'EL MENIA', 'domicile' => 1000, 'stopdesk' => 670]
        ];
        
        // Insert default values into database
        $db->beginTransaction();
        
        $stmt = $db->prepare("INSERT INTO delivery_costs (wilaya_code, wilaya_name, domicile, stopdesk) VALUES (:code, :name, :domicile, :stopdesk)");
        
        foreach ($wilayaData as $code => $data) {
            $stmt->bindParam(':code', $code);
            $stmt->bindParam(':name', $data['name']);
            $stmt->bindParam(':domicile', $data['domicile']);
            $stmt->bindParam(':stopdesk', $data['stopdesk']);
            $stmt->execute();
        }
        
        $db->commit();
        
        // Get the newly inserted delivery costs ordered by wilaya code numerically
        $stmt = $db->prepare("SELECT * FROM delivery_costs ORDER BY CAST(wilaya_code AS UNSIGNED) ASC");
        $stmt->execute();
        $deliveryCosts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    // Return delivery costs
    echo json_encode($deliveryCosts);
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error fetching delivery costs: ' . $e->getMessage()]);
}
