// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('Cart.js loaded and DOM fully loaded');
    
    // Initialize cart
    initCart();
    
    // Setup cart icon click event
    setupCartIcon();
});

// Setup cart icon
function setupCartIcon() {
    console.log('Setting up cart icon');
    const cartIcon = document.getElementById('cart-icon');
    
    if (cartIcon) {
        console.log('Cart icon found, adding event listener');
        cartIcon.addEventListener('click', function(e) {
            e.preventDefault(); // Prevent default navigation
            console.log('Cart icon clicked');
            showCart();
        });
    } else {
        console.error('Cart icon not found');
    }
}

// Show cart
function showCart() {
    console.log('Showing cart');
    
    // Create cart sidebar if it doesn't exist
    if (!document.getElementById('cart-sidebar')) {
        createCartSidebar();
    }
    
    // Show cart sidebar
    const cartSidebar = document.getElementById('cart-sidebar');
    if (cartSidebar) {
        console.log('Cart sidebar found, adding active class');
        cartSidebar.classList.add('active');
        updateCartDisplay();
        
        // Add overlay
        addOverlay();
    } else {
        console.error('Cart sidebar not found after creation attempt');
    }
}

// Hide cart
function hideCart() {
    console.log('Hiding cart');
    const cartSidebar = document.getElementById('cart-sidebar');
    if (cartSidebar) {
        console.log('Cart sidebar found, removing active class');
        cartSidebar.classList.remove('active');
        
        // Remove overlay
        removeOverlay();
    }
}

// Add overlay
function addOverlay() {
    console.log('Adding overlay');
    
    // Create overlay if it doesn't exist
    if (!document.getElementById('cart-overlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'cart-overlay';
        overlay.className = 'cart-overlay';
        overlay.addEventListener('click', hideCart);
        document.body.appendChild(overlay);
        
        // Add active class after a small delay to trigger transition
        setTimeout(() => {
            overlay.classList.add('active');
        }, 10);
    } else {
        // Just add active class if it already exists
        const overlay = document.getElementById('cart-overlay');
        overlay.classList.add('active');
    }
}

// Remove overlay
function removeOverlay() {
    console.log('Removing overlay');
    const overlay = document.getElementById('cart-overlay');
    if (overlay) {
        overlay.classList.remove('active');
        
        // Remove from DOM after transition
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }
}

// Create cart sidebar
function createCartSidebar() {
    console.log('Creating cart sidebar');
    
    // Remove existing sidebar if it exists
    const existingSidebar = document.getElementById('cart-sidebar');
    if (existingSidebar) {
        existingSidebar.parentNode.removeChild(existingSidebar);
    }
    
    // Create new sidebar
    const cartSidebar = document.createElement('div');
    cartSidebar.id = 'cart-sidebar';
    cartSidebar.className = 'cart-sidebar';
    
    cartSidebar.innerHTML = `
        <div class="cart-sidebar-header">
            <h3>Your Cart</h3>
            <button class="close-cart">&times;</button>
        </div>
        <div id="cart-items" class="cart-items"></div>
        <div class="cart-sidebar-footer">
            <div class="cart-total">Total: <span id="cart-total-amount">0.00 DZD</span></div>
            <a href="checkout.html" class="checkout-btn">Checkout</a>
        </div>
    `;
    
    document.body.appendChild(cartSidebar);
    console.log('Cart sidebar appended to body');
    
    // Add event listener to close button
    const closeBtn = cartSidebar.querySelector('.close-cart');
    if (closeBtn) {
        closeBtn.addEventListener('click', hideCart);
    }
    
    // Add event delegation for cart item buttons
    cartSidebar.addEventListener('click', function(e) {
        // Handle quantity decrease
        if (e.target.classList.contains('decrease')) {
            const productId = parseInt(e.target.getAttribute('data-id'));
            decreaseItemQuantity(productId);
        }
        
        // Handle quantity increase
        if (e.target.classList.contains('increase')) {
            const productId = parseInt(e.target.getAttribute('data-id'));
            increaseItemQuantity(productId);
        }
        
        // Handle remove item
        if (e.target.classList.contains('remove-item')) {
            const productId = parseInt(e.target.getAttribute('data-id'));
            removeCartItem(productId);
        }
    });
    
    return cartSidebar;
}

// Update cart display
function updateCartDisplay() {
    console.log('Updating cart display');
    const cartItems = document.getElementById('cart-items');
    const cartTotalAmount = document.getElementById('cart-total-amount');
    
    if (!cartItems || !cartTotalAmount) {
        console.error('Cart elements not found');
        return;
    }
    
    // Get cart from localStorage
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    console.log('Cart from localStorage in updateCartDisplay:', cart);

    if (cart.length === 0) {
        console.log('Cart is empty, showing empty message');
        cartItems.innerHTML = '<div class="empty-cart">Your cart is empty</div>';
        cartTotalAmount.textContent = '0.00 DZD';
        return;
    }
    
    // Generate cart items HTML
    let html = '';
    let total = 0;
    
    cart.forEach(item => {
        const itemTotal = item.price * item.quantity;
        total += itemTotal;
        
        html += `
            <div class="cart-item" data-id="${item.id}">
                <div class="cart-item-img">
                    <img src="${item.image}" alt="${item.name}">
                </div>
                <div class="cart-item-details">
                    <h4>${item.name}</h4>
                    <div class="cart-item-price">${item.price} DZD</div>
                    <div class="cart-item-quantity">
                        <button class="quantity-btn decrease" data-id="${item.id}">-</button>
                        <span>${item.quantity}</span>
                        <button class="quantity-btn increase" data-id="${item.id}">+</button>
                    </div>
                </div>
                <button class="remove-item" data-id="${item.id}">&times;</button>
            </div>
        `;
    });
    
    cartItems.innerHTML = html;
    cartTotalAmount.textContent = total.toFixed(2) + ' DZD';
}

// Initialize cart
function initCart() {
    console.log('Initializing cart');
    updateCartCount();
}

// Update cart count
function updateCartCount() {
    console.log('Updating cart count');
    const cartCount = document.getElementById('cart-count');
    
    if (!cartCount) {
        console.error('Cart count element not found');
        return;
    }
    
    // Get cart from localStorage
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Count total quantity of all items
    const totalQuantity = cart.reduce((total, item) => total + (item.quantity || 1), 0);

    // Update cart count
    cartCount.textContent = totalQuantity;
}

// Increase item quantity
function increaseItemQuantity(productId) {
    console.log('Increasing quantity for product:', productId);
    
    // Get cart from localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Find product in cart
    const productIndex = cart.findIndex(item => item.id === productId);
    
    if (productIndex !== -1) {
        // Increase quantity
        cart[productIndex].quantity += 1;
        
        // Save cart
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // Update cart display
        updateCartDisplay();
        
        // Update cart count
        updateCartCount();
    }
}

// Decrease item quantity
function decreaseItemQuantity(productId) {
    console.log('Decreasing quantity for product:', productId);
    
    // Get cart from localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Find product in cart
    const productIndex = cart.findIndex(item => item.id === productId);
    
    if (productIndex !== -1) {
        // Decrease quantity
        cart[productIndex].quantity -= 1;
        
        // If quantity is 0, remove product from cart
        if (cart[productIndex].quantity <= 0) {
            removeCartItem(productId);
            return;
        }
        
        // Save cart
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // Update cart display
        updateCartDisplay();
        
        // Update cart count
        updateCartCount();
    }
}

// Remove cart item
function removeCartItem(productId) {
    console.log('Removing product from cart:', productId);
    
    // Get cart from localStorage
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Remove product from cart
    cart = cart.filter(item => item.id !== productId);
    
    // Save cart
    localStorage.setItem('cart', JSON.stringify(cart));
    
    // Update cart display
    updateCartDisplay();
    
    // Update cart count
    updateCartCount();
}

// Add to cart function (for use from product pages)
function addToCart(productId, productName, productPrice, productImage) {
    console.log('Adding product to cart:', productId, productName, productPrice, productImage);
    
    // Get current cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Check if product already in cart
    const existingProductIndex = cart.findIndex(item => item.id === productId);
    
    if (existingProductIndex === -1) {
        // Add product to cart
        cart.push({
            id: productId,
            name: productName,
            price: productPrice,
            image: productImage,
            quantity: 1
        });
        
        // Save cart
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // Update cart count
        updateCartCount();
    }
}

