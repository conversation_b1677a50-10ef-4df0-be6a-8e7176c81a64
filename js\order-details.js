document.addEventListener('DOMContentLoaded', function() {
    // Get order ID from URL
    const urlParams = new URLSearchParams(window.location.search);
    const orderId = urlParams.get('id');
    
    if (!orderId) {
        window.location.href = 'orders.html';
        return;
    }
    
    // Set order ID in page
    document.getElementById('order-id').textContent = orderId;
    
    // Load order details
    loadOrderDetails(orderId);
    
    // Save status button
    document.getElementById('save-status').addEventListener('click', function() {
        updateOrderStatus(orderId);
    });
});

// Load order details
function loadOrderDetails(orderId) {
    fetch(`../backend/get_order_details.php?id=${orderId}`)
        .then(response => response.json())
        .then(data => {
            if (!data.order) {
                showNotification('Order not found', 'error');
                setTimeout(() => {
                    window.location.href = 'orders.html';
                }, 2000);
                return;
            }
            
            const order = data.order;
            const items = data.items;
            
            // Set order status
            document.getElementById('order-status').value = order.status;
            
            // Set customer info
            document.getElementById('customer-name').textContent = order.customer_name;
            document.getElementById('customer-phone').textContent = order.customer_phone;
            document.getElementById('customer-address').textContent = order.customer_address;
            document.getElementById('customer-city').textContent = order.customer_city;
            
            // Set order info
            document.getElementById('order-date').textContent = formatDate(order.order_date);
            document.getElementById('order-total').textContent = order.total_amount;
            document.getElementById('order-notes').textContent = order.notes || 'No notes';
            
            // Set order items
            const orderItemsTable = document.getElementById('order-items');
            let html = '';
            
            items.forEach(item => {
                html += `
                    <tr>
                        <td>
                            <div class="product-info">
                                <img src="../${item.image}" alt="${item.name}" class="product-thumbnail">
                                <span>${item.name}</span>
                            </div>
                        </td>
                        <td>${item.price} DZD</td>
                        <td>${item.quantity}</td>
                        <td>${item.price * item.quantity} DZD</td>
                    </tr>
                `;
            });
            
            orderItemsTable.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading order details:', error);
            showNotification('Failed to load order details', 'error');
        });
}

// Update order status
function updateOrderStatus(orderId) {
    const status = document.getElementById('order-status').value;
    
    fetch('../backend/update_order_status.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            id: orderId,
            status: status
        })
    })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showBeautifulNotification('Order Status Updated', `Order #${orderId} status changed to ${status}`, 'success');
            } else {
                showBeautifulNotification('Update Failed', data.message || 'Failed to update order status', 'error');
            }
        })
        .catch(error => {
            console.error('Error updating order status:', error);
            showBeautifulNotification('Update Failed', 'Failed to update order status', 'error');
        });
}

// Show beautiful notification
function showBeautifulNotification(title, message, type = 'success') {
    // Create notification container if it doesn't exist
    let notificationContainer = document.getElementById('notification-container');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        document.body.appendChild(notificationContainer);
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `beautiful-notification ${type}`;
    
    // Get icon based on type
    let icon = '';
    if (type === 'success') {
        icon = '<i class="fas fa-check-circle"></i>';
    } else if (type === 'error') {
        icon = '<i class="fas fa-exclamation-circle"></i>';
    } else if (type === 'info') {
        icon = '<i class="fas fa-info-circle"></i>';
    } else if (type === 'warning') {
        icon = '<i class="fas fa-exclamation-triangle"></i>';
    }
    
    // Set notification content
    notification.innerHTML = `
        <div class="notification-icon">
            ${icon}
        </div>
        <div class="notification-content">
            <h4>${title}</h4>
            <p>${message}</p>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
        <div class="notification-progress"></div>
    `;
    
    // Add notification to container
    notificationContainer.appendChild(notification);
    
    // Add active class after a small delay to trigger animation
    setTimeout(() => {
        notification.classList.add('active');
    }, 10);
    
    // Add progress animation
    const progress = notification.querySelector('.notification-progress');
    progress.style.animation = 'notification-progress 5s linear forwards';
    
    // Add close button event listener
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        closeNotification(notification);
    });
    
    // Auto close after 5 seconds
    setTimeout(() => {
        closeNotification(notification);
    }, 5000);
}

// Close notification
function closeNotification(notification) {
    // Remove active class to trigger exit animation
    notification.classList.remove('active');
    
    // Remove from DOM after animation completes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}
