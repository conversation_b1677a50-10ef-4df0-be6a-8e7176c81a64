<?php
require_once 'config.php';

// Check admin login
check_admin_login();

try {
    // Get admin data
    $adminId = $_SESSION['admin_id'];
    
    $stmt = $db->prepare("SELECT username FROM admins WHERE id = :id");
    $stmt->bindParam(':id', $adminId);
    $stmt->execute();
    
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$admin) {
        json_response(['success' => false, 'message' => 'Admin not found']);
    }
    
    json_response([
        'success' => true,
        'username' => $admin['username']
    ]);
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error fetching admin settings: ' . $e->getMessage()]);
}

