<?php
// Database configuration
$host = 'localhost';
$dbname = 'algerian_store'; // Changed from 'glam_algeria' to match README
$username = 'root';
$password = '';

// Create database connection
try {
    $db = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
} catch(PDOException $e) {
    echo json_encode(['success' => false, 'message' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Helper functions
function sanitize($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function json_response($data) {
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}

// Helper function to check admin login
function check_admin_login() {
    if (!isset($_SESSION['admin_id'])) {
        json_response(['success' => false, 'message' => 'Unauthorized access']);
        exit;
    }
}

// Helper function to log errors
function log_error($message, $file = null, $line = null) {
    $logFile = __DIR__ . '/../logs/error.log';
    $timestamp = date('Y-m-d H:i:s');
    $logMessage = "[$timestamp] $message";
    
    if ($file && $line) {
        $logMessage .= " in $file on line $line";
    }
    
    error_log($logMessage . PHP_EOL, 3, $logFile);
}




