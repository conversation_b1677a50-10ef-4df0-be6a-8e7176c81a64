<?php
require_once 'config.php';

// Get wilaya code
$wilayaCode = isset($_GET['wilaya']) ? sanitize($_GET['wilaya']) : '';

if (empty($wilayaCode)) {
    json_response(['success' => false, 'message' => 'Wilaya code is required']);
}

try {
    // Get delivery cost for wilaya
    $stmt = $db->prepare("SELECT * FROM delivery_costs WHERE wilaya_code = :code");
    $stmt->bindParam(':code', $wilayaCode);
    $stmt->execute();
    
    $deliveryCost = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$deliveryCost) {
        // Return default values if not found
        json_response([
            'success' => true,
            'domicile' => 1000,
            'stopdesk' => 700
        ]);
    }
    
    // Return delivery cost
    json_response([
        'success' => true,
        'domicile' => $deliveryCost['domicile'],
        'stopdesk' => $deliveryCost['stopdesk']
    ]);
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error fetching delivery cost: ' . $e->getMessage()]);
}