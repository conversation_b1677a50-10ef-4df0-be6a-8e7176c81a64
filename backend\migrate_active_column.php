<?php
require_once 'config.php';

try {
    // Check if active column exists
    $stmt = $db->query("SHOW COLUMNS FROM delivery_costs LIKE 'active'");
    $columnExists = $stmt->rowCount() > 0;
    
    if (!$columnExists) {
        echo "Adding 'active' column to delivery_costs table...\n";
        
        // Add the active column
        $db->exec("ALTER TABLE delivery_costs ADD COLUMN active TINYINT(1) NOT NULL DEFAULT 1 AFTER stopdesk");
        
        // Set all existing wilayas to active
        $db->exec("UPDATE delivery_costs SET active = 1");
        
        echo "Successfully added 'active' column and set all wilayas to active.\n";
    } else {
        echo "'active' column already exists.\n";
    }
    
    // Return success response for AJAX calls
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode(['success' => true, 'message' => 'Database migration completed']);
    }
    
} catch(PDOException $e) {
    $error = "Error migrating database: " . $e->getMessage();
    echo $error . "\n";
    
    // Return error response for AJAX calls
    if (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => $error]);
    }
}
?>
