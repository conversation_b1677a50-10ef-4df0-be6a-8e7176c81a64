<?php
require_once 'config.php';

// Always return <PERSON><PERSON><PERSON> for AJAX calls
header('Content-Type: application/json');

try {
    // Check if active column exists
    $stmt = $db->query("SHOW COLUMNS FROM delivery_costs LIKE 'active'");
    $columnExists = $stmt->rowCount() > 0;

    if (!$columnExists) {
        // Add the active column
        $db->exec("ALTER TABLE delivery_costs ADD COLUMN active TINYINT(1) NOT NULL DEFAULT 1 AFTER stopdesk");

        // Set all existing wilayas to active
        $db->exec("UPDATE delivery_costs SET active = 1");

        json_response(['success' => true, 'message' => 'Successfully added active column and set all wilayas to active']);
    } else {
        json_response(['success' => true, 'message' => 'Active column already exists']);
    }

} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error migrating database: ' . $e->getMessage()]);
}
?>
