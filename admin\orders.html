<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Orders - Glam Algeria Admin</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="shortcut icon" href="dashboard-icon.png" type="image/x-icon">
</head>
<body class="admin-page">
    <div class="admin-container">
        <aside class="sidebar">
            <div class="logo">
                <h2>Glam Admin</h2>
            </div>
            <nav class="admin-nav">
                <ul>
                    <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="orders.html" class="active"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="delivery.html"><i class="fas fa-truck"></i> Delivery</a></li>
                    <li><a href="settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <main class="admin-main">
            <header class="admin-header">
                <h1>Orders</h1>
                <div class="admin-header-right">
                    <button id="theme-toggle"><i class="fas fa-moon"></i></button>
                    <div class="admin-user">
                        <span id="admin-username">Admin</span>
                    </div>
                </div>
            </header>
            
            <div class="orders-list">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Order ID</th>
                            <th>Customer</th>
                            <th>Phone</th>
                            <th>Amount</th>
                            <th>Status</th>
                            <th>Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="orders-body">
                        <!-- Orders will be loaded dynamically -->
                    </tbody>
                </table>
            </div>
        </main>
    </div>
    
    <script src="../js/admin.js"></script>
    <script src="../js/orders.js"></script>
</body>
</html>

