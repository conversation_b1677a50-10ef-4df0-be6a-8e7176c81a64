<?php
require_once 'config.php';

// Check if user is logged in (admin or customer)
if (!isset($_SESSION['admin_id']) && !isset($_SESSION['user_id'])) {
    json_response(['success' => false, 'message' => 'Unauthorized']);
}

// Get order ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$id) {
    json_response(['success' => false, 'message' => 'Invalid order ID']);
}

try {
    // First check if order exists
    $stmt = $db->prepare("SELECT * FROM orders WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    
    $orderData = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$orderData) {
        json_response(['success' => false, 'message' => 'Order not found']);
    }
    
    // Get order items
    $stmt = $db->prepare("
        SELECT oi.*, p.name as product_name, p.image
        FROM order_items oi
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE oi.order_id = :id
    ");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // If no items found, still return the order with empty items array
    if (empty($items)) {
        $items = [];
    }
    
    // Format order data
    $order = [
        'id' => $orderData['id'],
        'customer_name' => $orderData['customer_name'],
        'customer_phone' => $orderData['customer_phone'],
        'customer_address' => $orderData['customer_address'],
        'customer_city' => $orderData['customer_city'],
        'delivery_type' => $orderData['delivery_type'] ?? 'domicile',
        'notes' => $orderData['notes'],
        'status' => $orderData['status'],
        'total_amount' => $orderData['total_amount'],
        'order_date' => $orderData['order_date']
    ];
    
    // Format items to ensure all have required fields
    $formattedItems = [];
    foreach ($items as $item) {
        $formattedItems[] = [
            'product_name' => $item['product_name'] ?? 'Unknown Product',
            'image' => $item['image'] ?? 'uploads/default-product.jpg',
            'quantity' => $item['quantity'] ?? 0,
            'price' => $item['price'] ?? 0
        ];
    }
    
    json_response([
        'success' => true, 
        'order' => $order, 
        'items' => $formattedItems
    ]);
    
} catch(PDOException $e) {
    // Log error for debugging
    error_log('Error in get_order_details.php: ' . $e->getMessage());
    
    json_response([
        'success' => false, 
        'message' => 'Error fetching order details: ' . $e->getMessage()
    ]);
}

