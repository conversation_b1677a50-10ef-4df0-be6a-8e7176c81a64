document.addEventListener('DOMContentLoaded', function() {
    // Load orders
    loadOrders();
    
    // Order details modal
    window.showOrderDetails = function(id) {
        if (!id) {
            showBeautifulNotification('Error', 'Invalid order ID', 'error');
            return;
        }
        
        // Show loading indicator
        const loadingModal = document.createElement('div');
        loadingModal.className = 'loading-modal';
        loadingModal.innerHTML = '<div class="loading-spinner"></div>';
        document.body.appendChild(loadingModal);
        
        fetch(`../backend/get_order_details.php?id=${id}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                // Remove loading indicator
                document.body.removeChild(loadingModal);
                
                if (!data.success) {
                    showBeautifulNotification('Error', data.message || 'Failed to load order details', 'error');
                    return;
                }
                
                if (!data.order || !data.items) {
                    showBeautifulNotification('Error', 'Invalid order data received', 'error');
                    return;
                }
                
                const order = data.order;
                const items = data.items;
                
                // Create modal with error handling for missing data
                createOrderDetailsModal(order, items);
            })
            .catch(error => {
                // Remove loading indicator
                if (document.querySelector('.loading-modal')) {
                    document.body.removeChild(loadingModal);
                }
                
                console.error('Error loading order details:', error);
                showBeautifulNotification('Error', 'Failed to load order details', 'error');
            });
    };
    
    // Update order status
    window.updateOrderStatus = function(id) {
        const status = document.getElementById('order-status').value;
        
        fetch('../backend/update_order_status.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                id: id,
                status: status
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show beautiful notification
                    showBeautifulNotification('Order Status Updated', `Order #${id} status changed to ${status}`, 'success');
                    
                    // Reload orders
                    loadOrders();
                } else {
                    showBeautifulNotification('Update Failed', data.message || 'Failed to update order status', 'error');
                }
            })
            .catch(error => {
                console.error('Error updating order status:', error);
                showBeautifulNotification('Update Failed', 'Failed to update order status', 'error');
            });
    };
});

// Load orders
function loadOrders() {
    fetch('../backend/get_orders.php')
        .then(response => response.json())
        .then(data => {
            const tableBody = document.getElementById('orders-body');
            
            if (!tableBody) return;
            
            // Check if data is an array (successful response) or an object (error response)
            if (!Array.isArray(data)) {
                console.error('Error loading orders:', data.message || 'Unknown error');
                showBeautifulNotification('Error', data.message || 'Failed to load orders', 'error');
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Error loading orders</td></tr>';
                return;
            }
            
            if (data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">No orders found</td></tr>';
                return;
            }
            
            let html = '';
            
            data.forEach(order => {
                // Make sure order has all required properties
                if (!order || !order.id) {
                    console.error('Invalid order data:', order);
                    return;
                }
                
                // Ensure all properties have default values if missing
                const safeOrder = {
                    id: order.id || 0,
                    customer_name: order.customer_name || 'Unknown',
                    customer_phone: order.customer_phone || 'N/A',
                    total_amount: order.total_amount || '0.00',
                    status: order.status || 'Pending',
                    order_date: order.order_date || new Date().toISOString()
                };
                
                // Add delete button only for cancelled orders
                const deleteButton = safeOrder.status === 'Cancelled' ? 
                    `<button class="admin-btn admin-btn-danger btn-sm" onclick="deleteOrder(${safeOrder.id})">
                        <i class="fas fa-trash"></i>
                    </button>` : '';
                
                html += `
                    <tr>
                        <td>#${safeOrder.id}</td>
                        <td>${safeOrder.customer_name}</td>
                        <td>${safeOrder.customer_phone}</td>
                        <td>${safeOrder.total_amount} DZD</td>
                        <td><span class="status-badge status-${safeOrder.status.toLowerCase()}">${safeOrder.status}</span></td>
                        <td>${formatDate(safeOrder.order_date)}</td>
                        <td>
                            <button class="admin-btn admin-btn-primary btn-sm" onclick="showOrderDetails(${safeOrder.id})">
                                <i class="fas fa-eye"></i>
                            </button>
                            ${deleteButton}
                        </td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading orders:', error);
            showBeautifulNotification('Error', 'Failed to load orders', 'error');
            const tableBody = document.getElementById('orders-body');
            if (tableBody) {
                tableBody.innerHTML = '<tr><td colspan="7" class="text-center">Error loading orders</td></tr>';
            }
        });
}

// Format date with error handling
function formatDate(dateString) {
    try {
        const date = new Date(dateString);
        // Check if date is valid
        if (isNaN(date.getTime())) {
            return 'Invalid date';
        }
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        console.error('Error formatting date:', error);
        return 'Invalid date';
    }
}

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        document.body.removeChild(notification);
    }, 3000);
}

// View order
function viewOrder(id) {
    window.location.href = `order-details.html?id=${id}`;
}

// Delete order
window.deleteOrder = function(id) {
    if (confirm('Are you sure you want to delete this order?')) {
        fetch(`../backend/delete_order.php?id=${id}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message || 'Order deleted successfully');
                    
                    // Reload orders
                    loadOrders();
                } else {
                    showNotification(data.message || 'Failed to delete order', 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting order:', error);
                showNotification('Failed to delete order', 'error');
            });
    }
};

// Show beautiful notification
function showBeautifulNotification(title, message, type = 'success') {
    // Create notification container if it doesn't exist
    let notificationContainer = document.getElementById('notification-container');
    if (!notificationContainer) {
        notificationContainer = document.createElement('div');
        notificationContainer.id = 'notification-container';
        document.body.appendChild(notificationContainer);
    }
    
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `beautiful-notification ${type}`;
    
    // Get icon based on type
    let icon = '';
    if (type === 'success') {
        icon = '<i class="fas fa-check-circle"></i>';
    } else if (type === 'error') {
        icon = '<i class="fas fa-exclamation-circle"></i>';
    } else if (type === 'info') {
        icon = '<i class="fas fa-info-circle"></i>';
    } else if (type === 'warning') {
        icon = '<i class="fas fa-exclamation-triangle"></i>';
    }
    
    // Set notification content
    notification.innerHTML = `
        <div class="notification-icon">
            ${icon}
        </div>
        <div class="notification-content">
            <h4>${title}</h4>
            <p>${message}</p>
        </div>
        <button class="notification-close">
            <i class="fas fa-times"></i>
        </button>
        <div class="notification-progress"></div>
    `;
    
    // Add notification to container
    notificationContainer.appendChild(notification);
    
    // Add active class after a small delay to trigger animation
    setTimeout(() => {
        notification.classList.add('active');
    }, 10);
    
    // Add progress animation
    const progress = notification.querySelector('.notification-progress');
    progress.style.animation = 'notification-progress 5s linear forwards';
    
    // Add close button event listener
    const closeButton = notification.querySelector('.notification-close');
    closeButton.addEventListener('click', () => {
        closeNotification(notification);
    });
    
    // Auto close after 5 seconds
    setTimeout(() => {
        closeNotification(notification);
    }, 5000);
}

// Close notification
function closeNotification(notification) {
    // Remove active class to trigger exit animation
    notification.classList.remove('active');
    
    // Remove from DOM after animation completes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}

// Create order details modal with error handling
function createOrderDetailsModal(order, items) {
    // Wilaya names mapping
    const wilayaNames = {
        '1': 'ADRAR', '2': 'CHLEF', '3': 'LAGHOUAT', '4': 'OUM EL BOUAGHI', '5': 'BATNA',
        '6': 'BEJAIA', '7': 'BISKRA', '8': 'BECHAR', '9': 'BLIDA', '10': 'BOUIRA',
        '11': 'TAMANRASSET', '12': 'TEBESSA', '13': 'TLEMCEN', '14': 'TIARET', '15': 'TIZI OUZOU',
        '16': 'ALGER', '17': 'DJELFA', '18': 'JIJEL', '19': 'SETIF', '20': 'SAIDA',
        '21': 'SKIKDA', '22': 'SIDI BEL ABBES', '23': 'ANNABA', '24': 'GUELMA', '25': 'CONSTANTINE',
        '26': 'MEDEA', '27': 'MOSTAGANEM', '28': 'M\'SILA', '29': 'MASCARA', '30': 'OUARGLA',
        '31': 'ORAN', '32': 'EL BAYADH', '33': 'ILLIZI', '34': 'BORDJ BOU ARRERIDJ', '35': 'BOUMERDES',
        '36': 'EL TARF', '37': 'TINDOUF', '38': 'TISSEMSILT', '39': 'EL OUED', '40': 'KHENCHELA',
        '41': 'SOUK AHRAS', '42': 'TIPAZA', '43': 'MILA', '44': 'AIN DEFLA', '45': 'NAAMA',
        '46': 'AIN TEMOUCHENT', '47': 'GHARDAIA', '48': 'RELIZANE', '49': 'TIMIMOUN', '51': 'OULED DJELLAL',
        '52': 'BENI ABBES', '53': 'IN SALAH', '54': 'IN GUEZZAM', '55': 'TOUGGOURT', '57': 'M\'GHAIR', '58': 'EL MENIA'
    };
    
    // Get wilaya name with fallback
    const wilayaName = wilayaNames[order.customer_city] || order.customer_city || 'Unknown';
    
    // Determine delivery type display
    const deliveryTypeDisplay = order.delivery_type === 'stopdesk' ? 'Stop Desk' : 'Home Delivery';
    
    // Create modal
    const modal = document.createElement('div');
    modal.className = 'modal';
    
    // Process items with error handling
    let itemsHtml = '';
    let totalAmount = 0;
    
    if (Array.isArray(items) && items.length > 0) {
        items.forEach(item => {
            if (!item) return;
            
            const quantity = parseFloat(item.quantity) || 0;
            const price = parseFloat(item.price) || 0;
            const itemTotal = price * quantity;
            totalAmount += itemTotal;
            
            itemsHtml += `
                <tr>
                    <td>${item.product_name || 'Unknown Product'}</td>
                    <td>${quantity}</td>
                    <td>${price.toFixed(2)} DZD</td>
                    <td>${itemTotal.toFixed(2)} DZD</td>
                </tr>
            `;
        });
    } else {
        itemsHtml = '<tr><td colspan="4">No items found</td></tr>';
    }
    
    // Build modal HTML
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h2>Order #${order.id}</h2>
                <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
                <div class="order-details">
                    <div class="order-info">
                        <h3>Order Information</h3>
                        <div class="info-row">
                            <div class="info-label">Order ID:</div>
                            <div class="info-value">#${order.id}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Date:</div>
                            <div class="info-value">${formatDate(order.order_date)}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Status:</div>
                            <div class="info-value">
                                <select id="order-status" class="status-select">
                                    <option value="Pending" ${order.status === 'Pending' ? 'selected' : ''}>Pending</option>
                                    <option value="Processing" ${order.status === 'Processing' ? 'selected' : ''}>Processing</option>
                                    <option value="Completed" ${order.status === 'Completed' ? 'selected' : ''}>Completed</option>
                                    <option value="Cancelled" ${order.status === 'Cancelled' ? 'selected' : ''}>Cancelled</option>
                                </select>
                                <button class="admin-btn admin-btn-primary btn-sm" onclick="updateOrderStatus(${order.id})">Update</button>
                            </div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Total Amount:</div>
                            <div class="info-value">${order.total_amount || totalAmount.toFixed(2)} DZD</div>
                        </div>
                    </div>
                    <div class="customer-info">
                        <h3>Customer Information</h3>
                        <div class="info-row">
                            <div class="info-label">Name:</div>
                            <div class="info-value">${order.customer_name || 'N/A'}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Phone:</div>
                            <div class="info-value">${order.customer_phone || 'N/A'}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Address:</div>
                            <div class="info-value">${order.customer_address || 'N/A'}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Wilaya:</div>
                            <div class="info-value">${wilayaName}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Delivery Type:</div>
                            <div class="info-value">${deliveryTypeDisplay}</div>
                        </div>
                        <div class="info-row">
                            <div class="info-label">Notes:</div>
                            <div class="info-value">${order.notes || 'N/A'}</div>
                        </div>
                    </div>
                </div>
                <div class="order-items">
                    <h3>Order Items</h3>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Product</th>
                                <th>Quantity</th>
                                <th>Price</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${itemsHtml}
                        </tbody>
                        <tfoot>
                            <tr>
                                <td colspan="3" style="text-align: right;"><strong>Total:</strong></td>
                                <td><strong>${totalAmount.toFixed(2)} DZD</strong></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // Show modal
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
    
    // Close modal
    const closeBtn = modal.querySelector('.close-modal');
    closeBtn.addEventListener('click', function() {
        modal.classList.remove('show');
        
        setTimeout(() => {
            document.body.removeChild(modal);
        }, 300);
    });
}

// Close notification
function closeNotification(notification) {
    // Remove active class to trigger exit animation
    notification.classList.remove('active');
    
    // Remove from DOM after animation completes
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 300);
}



