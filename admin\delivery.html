<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery Settings - Glam Algeria Admin</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="admin-page">
    <div class="admin-container">
        <aside class="sidebar">
            <div class="logo">
                <h2>Glam Admin</h2>
            </div>
            <nav class="admin-nav">
                <ul>
                    <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="delivery.html" class="active"><i class="fas fa-truck"></i> Delivery</a></li>
                    <li><a href="settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <main class="admin-main">
            <header class="admin-header">
                <h1>Delivery Settings</h1>
                <div class="admin-header-right">
                    <button id="theme-toggle"><i class="fas fa-moon"></i></button>
                    <div class="admin-user">
                        <span id="admin-username">Admin</span>
                    </div>
                </div>
            </header>
            
            <div class="admin-actions">
                <button id="add-wilaya-btn" class="admin-btn admin-btn-success">
                    <i class="fas fa-plus"></i> Add New Wilaya
                </button>
                <button id="save-all-btn" class="admin-btn admin-btn-primary">
                    <i class="fas fa-save"></i> Save All Changes
                </button>
            </div>
            
            <div class="delivery-settings">
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h3>Shipping Costs by Wilaya</h3>
                    </div>
                    <div class="admin-card-body">
                        <table class="admin-table" id="delivery-table">
                            <thead>
                                <tr>
                                    <th>Wilaya Code</th>
                                    <th>Wilaya Name</th>
                                    <th>Home Delivery (DZD)</th>
                                    <th>StopDesk (DZD)</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="delivery-body">
                                <!-- Delivery costs will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Edit Modal -->
            <div id="edit-modal" class="modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Edit Shipping Cost</h2>
                        <span class="close-modal">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="edit-form">
                            <input type="hidden" id="edit-wilaya-code" name="wilaya_code">
                            
                            <div class="form-group">
                                <label for="edit-wilaya-name">Wilaya</label>
                                <input type="text" id="edit-wilaya-name" name="wilaya_name" readonly>
                            </div>
                            
                            <div class="form-group">
                                <label for="edit-domicile">Home Delivery Cost (DZD)</label>
                                <input type="number" id="edit-domicile" name="domicile" min="0" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="edit-stopdesk">StopDesk Cost (DZD)</label>
                                <input type="number" id="edit-stopdesk" name="stopdesk" min="0" required>
                                <small>Set to 0 if StopDesk is not available in this wilaya</small>
                            </div>
                            
                            <div class="form-buttons">
                                <button type="button" class="admin-btn admin-btn-secondary" id="cancel-edit">Cancel</button>
                                <button type="submit" class="admin-btn admin-btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Add Wilaya Modal -->
            <div id="add-modal" class="modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
                <div class="modal-content" style="background-color: white; border-radius: 8px; width: 90%; max-width: 600px; max-height: 90vh; overflow-y: auto; box-shadow: 0 5px 15px rgba(0,0,0,0.3);">
                    <div class="modal-header">
                        <h2>Add New Wilaya</h2>
                        <span class="close-modal">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="add-form">
                            <div class="form-group">
                                <label for="add-wilaya-code">Wilaya Code</label>
                                <input type="text" id="add-wilaya-code" name="wilaya_code" required
                                       placeholder="e.g., 59" maxlength="2">
                                <small>Enter a unique 2-digit code (01-99)</small>
                            </div>

                            <div class="form-group">
                                <label for="add-wilaya-name">Wilaya Name</label>
                                <input type="text" id="add-wilaya-name" name="wilaya_name" required
                                       placeholder="e.g., New Wilaya">
                            </div>

                            <div class="form-group">
                                <label for="add-domicile">Home Delivery Cost (DZD)</label>
                                <input type="number" id="add-domicile" name="domicile" min="0" step="1" required
                                       placeholder="e.g., 500">
                            </div>

                            <div class="form-group">
                                <label for="add-stopdesk">StopDesk Cost (DZD)</label>
                                <input type="number" id="add-stopdesk" name="stopdesk" min="0" step="1" required
                                       placeholder="e.g., 300">
                            </div>

                            <div class="form-buttons">
                                <button type="button" class="admin-btn admin-btn-secondary" id="cancel-add">Cancel</button>
                                <button type="submit" class="admin-btn admin-btn-success">Add Wilaya</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="../js/admin.js"></script>
    <script src="../js/delivery.js"></script>
</body>
</html>