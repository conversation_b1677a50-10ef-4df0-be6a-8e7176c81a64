<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery Settings - Glam Algeria Admin</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="admin-page">
    <div class="admin-container">
        <aside class="sidebar">
            <div class="logo">
                <h2>Glam Admin</h2>
            </div>
            <nav class="admin-nav">
                <ul>
                    <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="delivery.html" class="active"><i class="fas fa-truck"></i> Delivery</a></li>
                    <li><a href="settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <main class="admin-main">
            <header class="admin-header">
                <h1>Delivery Settings</h1>
                <div class="admin-header-right">
                    <button id="theme-toggle"><i class="fas fa-moon"></i></button>
                    <div class="admin-user">
                        <span id="admin-username">Admin</span>
                    </div>
                </div>
            </header>
            
            <div class="admin-actions">
                <button id="save-all-btn" class="admin-btn admin-btn-primary">
                    <i class="fas fa-save"></i> Save All Changes
                </button>
            </div>
            
            <div class="delivery-settings">
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h3>Shipping Costs by Wilaya</h3>
                    </div>
                    <div class="admin-card-body">
                        <table class="admin-table" id="delivery-table">
                            <thead>
                                <tr>
                                    <th>Wilaya Code</th>
                                    <th>Wilaya Name</th>
                                    <th>Home Delivery (DZD)</th>
                                    <th>StopDesk (DZD)</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="delivery-body">
                                <!-- Delivery costs will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <!-- Edit Modal -->
            <div id="edit-modal" class="modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h2>Edit Shipping Cost</h2>
                        <span class="close-modal">&times;</span>
                    </div>
                    <div class="modal-body">
                        <form id="edit-form">
                            <input type="hidden" id="edit-wilaya-code" name="wilaya_code">
                            
                            <div class="form-group">
                                <label for="edit-wilaya-name">Wilaya</label>
                                <input type="text" id="edit-wilaya-name" name="wilaya_name" readonly>
                            </div>
                            
                            <div class="form-group">
                                <label for="edit-domicile">Home Delivery Cost (DZD)</label>
                                <input type="number" id="edit-domicile" name="domicile" min="0" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="edit-stopdesk">StopDesk Cost (DZD)</label>
                                <input type="number" id="edit-stopdesk" name="stopdesk" min="0" required>
                                <small>Set to 0 if StopDesk is not available in this wilaya</small>
                            </div>
                            
                            <div class="form-buttons">
                                <button type="button" class="admin-btn admin-btn-secondary" id="cancel-edit">Cancel</button>
                                <button type="submit" class="admin-btn admin-btn-primary">Save Changes</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../js/admin.js"></script>
    <script src="../js/delivery.js"></script>
</body>
</html>