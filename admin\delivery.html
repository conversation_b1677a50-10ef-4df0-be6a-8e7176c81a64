<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Delivery Settings - Glam Algeria Admin</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="admin-page">
    <div class="admin-container">
        <aside class="sidebar">
            <div class="logo">
                <h2>Glam Admin</h2>
            </div>
            <nav class="admin-nav">
                <ul>
                    <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="delivery.html" class="active"><i class="fas fa-truck"></i> Delivery</a></li>
                    <li><a href="settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <main class="admin-main">
            <header class="admin-header">
                <h1>Delivery Settings</h1>
                <div class="admin-header-right">
                    <button id="theme-toggle"><i class="fas fa-moon"></i></button>
                    <div class="admin-user">
                        <span id="admin-username">Admin</span>
                    </div>
                </div>
            </header>
            
            <div class="admin-actions">
                <button id="add-wilaya-btn" class="admin-btn admin-btn-success" onclick="showAddWilayaModal();">
                    <i class="fas fa-plus"></i> Add New Wilaya
                </button>
                <button id="save-all-btn" class="admin-btn admin-btn-primary">
                    <i class="fas fa-save"></i> Save All Changes
                </button>
            </div>
            
            <div class="delivery-settings">
                <div class="admin-card">
                    <div class="admin-card-header">
                        <h3>Shipping Costs by Wilaya</h3>
                    </div>
                    <div class="admin-card-body">
                        <table class="admin-table" id="delivery-table">
                            <thead>
                                <tr>
                                    <th>Wilaya Code</th>
                                    <th>Wilaya Name</th>
                                    <th>Home Delivery (DZD)</th>
                                    <th>StopDesk (DZD)</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="delivery-body">
                                <!-- Delivery costs will be loaded dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            


            <!-- Add Wilaya Modal - Simple Version -->
            <div id="add-wilaya-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.7); z-index: 9999;">
                <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 30px; border-radius: 10px; width: 500px; max-width: 90%; box-shadow: 0 10px 30px rgba(0,0,0,0.3);">
                    <h2 style="margin-top: 0; color: #333; text-align: center;">Add New Wilaya</h2>

                    <form id="add-wilaya-form" style="margin-top: 20px;" onsubmit="submitNewWilaya(); return false;">
                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #555;">Wilaya Code:</label>
                            <input type="text" id="new-wilaya-code" name="wilaya_code" required
                                   placeholder="e.g., 59" maxlength="2"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px;">
                            <small style="color: #666;">Enter a unique 2-digit code (01-99)</small>
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #555;">Wilaya Name:</label>
                            <input type="text" id="new-wilaya-name" name="wilaya_name" required
                                   placeholder="e.g., New Wilaya"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px;">
                        </div>

                        <div style="margin-bottom: 15px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #555;">Home Delivery Cost (DZD):</label>
                            <input type="number" id="new-domicile" name="domicile" min="0" step="1" required
                                   placeholder="e.g., 500"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px;">
                        </div>

                        <div style="margin-bottom: 20px;">
                            <label style="display: block; margin-bottom: 5px; font-weight: bold; color: #555;">StopDesk Cost (DZD):</label>
                            <input type="number" id="new-stopdesk" name="stopdesk" min="0" step="1" required
                                   placeholder="e.g., 300"
                                   style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 14px;">
                        </div>

                        <div style="text-align: center;">
                            <button type="button" onclick="document.getElementById('add-wilaya-modal').style.display='none';"
                                    style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-right: 10px; cursor: pointer;">
                                Cancel
                            </button>
                            <button type="submit"
                                    style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;">
                                Add Wilaya
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script src="../js/admin.js"></script>
    <script src="../js/delivery.js"></script>

    <script>
        // Simple modal functions
        function showAddWilayaModal() {
            const modal = document.getElementById('add-wilaya-modal');
            if (modal) {
                modal.style.display = 'block';
                // Clear form
                document.getElementById('new-wilaya-code').value = '';
                document.getElementById('new-wilaya-name').value = '';
                document.getElementById('new-domicile').value = '';
                document.getElementById('new-stopdesk').value = '';
            }
        }

        function closeAddModal() {
            document.getElementById('add-wilaya-modal').style.display = 'none';
        }

        function submitNewWilaya() {
            const wilayaCode = document.getElementById('new-wilaya-code').value;
            const wilayaName = document.getElementById('new-wilaya-name').value;
            const domicile = document.getElementById('new-domicile').value;
            const stopdesk = document.getElementById('new-stopdesk').value;

            // Basic validation
            if (!wilayaCode || !wilayaName || !domicile || !stopdesk) {
                alert('Please fill in all fields');
                return;
            }

            // Create form data
            const formData = new FormData();
            formData.append('wilaya_code', wilayaCode);
            formData.append('wilaya_name', wilayaName);
            formData.append('domicile', domicile);
            formData.append('stopdesk', stopdesk);

            // Submit to backend
            fetch('../backend/add_delivery_cost.php', {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('Response data:', data);
                if (data.success) {
                    alert('Wilaya added successfully!');
                    closeAddModal();
                    // Reload the page to refresh the table
                    window.location.reload();
                } else {
                    alert('Error: ' + (data.message || 'Failed to add wilaya'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to add wilaya. Please try again.');
            });
        }

        // Make functions globally accessible
        window.showAddWilayaModal = showAddWilayaModal;
        window.closeAddModal = closeAddModal;
        window.submitNewWilaya = submitNewWilaya;
    </script>
</body>
</html>