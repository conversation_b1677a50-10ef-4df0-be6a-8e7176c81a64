<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Get order ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$id) {
    json_response(['success' => false, 'message' => 'Invalid order ID']);
}

try {
    // Start transaction
    $db->beginTransaction();
    
    // Check if order exists and is cancelled
    $stmt = $db->prepare("SELECT status FROM orders WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    
    $order = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$order) {
        json_response(['success' => false, 'message' => 'Order not found']);
    }
    
    if ($order['status'] !== 'Cancelled') {
        json_response(['success' => false, 'message' => 'Only cancelled orders can be deleted']);
    }
    
    // Delete order items first (due to foreign key constraint)
    $stmt = $db->prepare("DELETE FROM order_items WHERE order_id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    
    // Delete order
    $stmt = $db->prepare("DELETE FROM orders WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    
    // Commit transaction
    $db->commit();
    
    json_response(['success' => true, 'message' => 'Order deleted successfully']);
} catch(PDOException $e) {
    // Rollback transaction on error
    $db->rollBack();
    json_response(['success' => false, 'message' => 'Error deleting order: ' . $e->getMessage()]);
}