<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    json_response(['success' => false, 'message' => 'Invalid request method']);
}

// Get form data
$wilaya_code = isset($_POST['wilaya_code']) ? sanitize($_POST['wilaya_code']) : '';
$wilaya_name = isset($_POST['wilaya_name']) ? sanitize($_POST['wilaya_name']) : '';
$domicile = isset($_POST['domicile']) ? intval($_POST['domicile']) : 0;
$stopdesk = isset($_POST['stopdesk']) ? intval($_POST['stopdesk']) : 0;

// Validate form data
if (empty($wilaya_code) || empty($wilaya_name) || $domicile < 0 || $stopdesk < 0) {
    json_response(['success' => false, 'message' => 'Please fill in all fields with valid values']);
}

// Validate wilaya code format (should be 2 digits)
if (!preg_match('/^[0-9]{1,2}$/', $wilaya_code)) {
    json_response(['success' => false, 'message' => 'Wilaya code must be 1-2 digits']);
}

// Pad wilaya code to 2 digits if needed
$wilaya_code = str_pad($wilaya_code, 2, '0', STR_PAD_LEFT);

try {
    // Check if wilaya code already exists
    $stmt = $db->prepare("SELECT COUNT(*) FROM delivery_costs WHERE wilaya_code = :wilaya_code");
    $stmt->bindParam(':wilaya_code', $wilaya_code);
    $stmt->execute();
    
    if ($stmt->fetchColumn() > 0) {
        json_response(['success' => false, 'message' => 'Wilaya code already exists']);
    }
    
    // Check if wilaya name already exists
    $stmt = $db->prepare("SELECT COUNT(*) FROM delivery_costs WHERE wilaya_name = :wilaya_name");
    $stmt->bindParam(':wilaya_name', $wilaya_name);
    $stmt->execute();
    
    if ($stmt->fetchColumn() > 0) {
        json_response(['success' => false, 'message' => 'Wilaya name already exists']);
    }
    
    // Insert new wilaya (active by default)
    $stmt = $db->prepare("INSERT INTO delivery_costs (wilaya_code, wilaya_name, domicile, stopdesk, active) VALUES (:wilaya_code, :wilaya_name, :domicile, :stopdesk, 1)");
    $stmt->bindParam(':wilaya_code', $wilaya_code);
    $stmt->bindParam(':wilaya_name', $wilaya_name);
    $stmt->bindParam(':domicile', $domicile);
    $stmt->bindParam(':stopdesk', $stopdesk);
    $stmt->execute();
    
    json_response(['success' => true, 'message' => 'Wilaya added successfully']);
    
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error adding wilaya: ' . $e->getMessage()]);
}
?>
