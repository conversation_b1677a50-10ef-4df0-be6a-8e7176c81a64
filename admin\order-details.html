<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Details - Finix Admin</title>
    <link rel="stylesheet" href="../css/style.css?v=2.5">
    <link rel="stylesheet" href="../css/admin.css?v=2.5">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="shortcut icon" href="dashboard-icon.png" type="image/x-icon">
    <script>
        // Apply dark mode immediately if saved
        if (localStorage.getItem('admin-theme') === 'dark') {
            document.documentElement.classList.add('dark-mode');
        }
    </script>
</head>
<body class="admin-page">
    <div class="admin-container">
        <aside class="sidebar">
            <div class="logo">
                <h2>Finix Admin</h2>
            </div>
            <nav class="admin-nav">
                <ul>
                    <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="orders.html" class="active"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <main class="admin-content">
            <header class="admin-header">
                <div class="admin-header-left">
                    <h1>Order Details</h1>
                </div>
                <div class="admin-header-right">
                    <button id="theme-toggle"><i class="fas fa-moon"></i></button>
                    <div class="admin-profile">
                        <span id="admin-name">Admin</span>
                    </div>
                </div>
            </header>
            
            <div class="dashboard-section">
                <div class="order-details-header">
                    <h2>Order #<span id="order-id"></span></h2>
                    <div class="order-status">
                        <label for="order-status">Status:</label>
                        <select id="order-status">
                            <option value="Pending">Pending</option>
                            <option value="Processing">Processing</option>
                            <option value="Completed">Completed</option>
                            <option value="Cancelled">Cancelled</option>
                        </select>
                    </div>
                </div>
                
                <div class="order-details-grid">
                    <div class="order-info-card">
                        <h3>Customer Information</h3>
                        <div class="order-info-content">
                            <p><strong>Name:</strong> <span id="customer-name"></span></p>
                            <p><strong>Phone:</strong> <span id="customer-phone"></span></p>
                            <p><strong>Address:</strong> <span id="customer-address"></span></p>
                            <p><strong>City:</strong> <span id="customer-city"></span></p>
                        </div>
                    </div>
                    
                    <div class="order-info-card">
                        <h3>Order Information</h3>
                        <div class="order-info-content">
                            <p><strong>Order Date:</strong> <span id="order-date"></span></p>
                            <p><strong>Total Amount:</strong> <span id="order-total"></span> DZD</p>
                            <p><strong>Notes:</strong> <span id="order-notes"></span></p>
                        </div>
                    </div>
                </div>
                
                <div class="order-items-section">
                    <h3>Order Items</h3>
                    <div class="table-container">
                        <table class="admin-table">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Price</th>
                                    <th>Quantity</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody id="order-items">
                                <!-- Order items will be loaded here -->
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <div class="order-actions">
                    <a href="orders.html" class="btn"><i class="fas fa-arrow-left"></i> Back to Orders</a>
                    <button id="save-status" class="btn btn-primary">Save Changes</button>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../js/admin.js?v=2.5"></script>
    <script src="../js/order-details.js?v=2.5"></script>
</body>
</html>
