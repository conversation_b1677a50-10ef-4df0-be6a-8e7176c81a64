<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Validate form data
if (!isset($_POST['username'])) {
    json_response(['success' => false, 'message' => 'Missing required fields']);
}

$adminId = $_SESSION['admin_id'];
$username = sanitize($_POST['username']);
$currentPassword = isset($_POST['current_password']) ? $_POST['current_password'] : '';
$newPassword = isset($_POST['new_password']) ? $_POST['new_password'] : '';
$confirmPassword = isset($_POST['confirm_password']) ? $_POST['confirm_password'] : '';

// Validate username
if (empty($username)) {
    json_response(['success' => false, 'message' => 'Username cannot be empty']);
}

try {
    // Start transaction
    $db->beginTransaction();
    
    // Check if username already exists
    $stmt = $db->prepare("SELECT id FROM admins WHERE username = :username AND id != :id");
    $stmt->bindParam(':username', $username);
    $stmt->bindParam(':id', $adminId);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        json_response(['success' => false, 'message' => 'Username already exists']);
    }
    
    // Update username
    $stmt = $db->prepare("UPDATE admins SET username = :username WHERE id = :id");
    $stmt->bindParam(':username', $username);
    $stmt->bindParam(':id', $adminId);
    $stmt->execute();
    
    // Update password if provided
    if (!empty($currentPassword) && !empty($newPassword)) {
        // Check if passwords match
        if ($newPassword !== $confirmPassword) {
            $db->rollBack();
            json_response(['success' => false, 'message' => 'New passwords do not match']);
        }
        
        // Check current password
        $stmt = $db->prepare("SELECT password FROM admins WHERE id = :id");
        $stmt->bindParam(':id', $adminId);
        $stmt->execute();
        
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!password_verify($currentPassword, $admin['password'])) {
            $db->rollBack();
            json_response(['success' => false, 'message' => 'Current password is incorrect']);
        }
        
        // Hash new password
        $hashedPassword = password_hash($newPassword, PASSWORD_DEFAULT);
        
        // Update password
        $stmt = $db->prepare("UPDATE admins SET password = :password WHERE id = :id");
        $stmt->bindParam(':password', $hashedPassword);
        $stmt->bindParam(':id', $adminId);
        $stmt->execute();
    }
    
    // Commit transaction
    $db->commit();
    
    // Update session username
    $_SESSION['admin_username'] = $username;
    
    // Return success response
    json_response(['success' => true, 'message' => 'Settings updated successfully']);
} catch(PDOException $e) {
    // Rollback transaction on error
    $db->rollBack();
    json_response(['success' => false, 'message' => 'Error updating settings: ' . $e->getMessage()]);
}


