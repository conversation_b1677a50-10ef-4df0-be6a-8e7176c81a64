<?php
// Test the get_products.php endpoint
echo "Testing get_products.php endpoint...\n";

// Include the get_products.php file and capture its output
ob_start();
include 'backend/get_products.php';
$output = ob_get_clean();

echo "Output from get_products.php:\n";
echo $output . "\n";

// Test if it's valid JSON
$data = json_decode($output, true);
if (json_last_error() === JSON_ERROR_NONE) {
    echo "Valid JSON response!\n";
    if (is_array($data)) {
        echo "Number of products returned: " . count($data) . "\n";
    } else {
        echo "Response is not an array. Content: " . print_r($data, true) . "\n";
    }
} else {
    echo "Invalid JSON response. Error: " . json_last_error_msg() . "\n";
}
?>
