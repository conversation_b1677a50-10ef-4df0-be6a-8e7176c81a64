<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Products - Glam Algeria Admin</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="shortcut icon" href="dashboard-icon.png" type="image/x-icon">
</head>
<body class="admin-page">
    <div class="admin-container">
        <aside class="sidebar">
            <div class="logo">
                <h2>Glam Admin</h2>
            </div>
            <nav class="admin-nav">
                <ul>
                    <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html" class="active"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="delivery.html"><i class="fas fa-truck"></i> Delivery</a></li>
                    <li><a href="settings.html"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <main class="admin-main">
            <header class="admin-header">
                <h1>Products</h1>
                <div class="admin-header-right">
                    <button id="theme-toggle"><i class="fas fa-moon"></i></button>
                    <div class="admin-user">
                        <span id="admin-username">Admin</span>
                    </div>
                </div>
            </header>
            
            <div class="admin-actions">
                <button id="add-product-btn" class="admin-btn admin-btn-primary">
                    <i class="fas fa-plus"></i> Add New Product
                </button>
            </div>
            
            <div class="products-list">
                <table class="admin-table">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Name</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Stock</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="products-body">
                        <!-- Products will be loaded dynamically -->
                    </tbody>
                </table>
            </div>
            
            <div id="product-form-container" class="admin-form-container" style="display: none;">
                <div class="admin-form">
                    <h2 id="product-form-title">Edit Product</h2>
                    <form id="product-form" enctype="multipart/form-data">
                        <input type="hidden" id="product-id" name="id" value="">
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-name">Product Name</label>
                                <input type="text" id="product-name" name="name" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="product-category">Category</label>
                                <select id="product-category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="electronics">Electronics</option>
                                    <option value="clothing">Clothing</option>
                                    <option value="home">Home & Decor</option>
                                    <option value="accessories">Accessories</option>
                                    <option value="others">Others</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="product-price">Price (DZD)</label>
                                <input type="number" id="product-price" name="price" min="0" step="0.01" required>
                            </div>
                            
                            <div class="form-group">
                                <label for="product-stock">Stock</label>
                                <input type="number" id="product-stock" name="stock" min="0" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="product-description">Description</label>
                            <textarea id="product-description" name="description" rows="3"></textarea>
                        </div>
                        
                        <div class="form-group image-upload-container">
                            <label for="product-image">Product Image</label>
                            <div class="image-upload-wrapper">
                                <div id="product-image-preview" class="image-preview"></div>
                                <div class="upload-controls">
                                    <input type="file" id="product-image" name="image" accept="image/*">
                                    <label for="product-image" class="upload-btn">
                                        <i class="fas fa-upload"></i> Choose Image
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-buttons">
                            <button type="button" class="admin-btn admin-btn-secondary" onclick="document.getElementById('product-form-container').style.display = 'none';">Cancel</button>
                            <button type="submit" class="admin-btn admin-btn-primary">Save Product</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>
    
    <script src="../js/admin.js"></script>
    <script src="../js/products.js"></script>
</body>
</html>




