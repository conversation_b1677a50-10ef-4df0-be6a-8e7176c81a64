document.addEventListener('DOMContentLoaded', function() {
    // Load delivery costs
    loadDeliveryCosts();

    // Setup event listeners
    document.getElementById('save-all-btn').addEventListener('click', saveAllChanges);
    document.getElementById('add-wilaya-btn').addEventListener('click', showAddModal);

    // Setup edit modal close events
    const editModal = document.getElementById('edit-modal');
    const editCloseBtn = editModal.querySelector('.close-modal');
    const editCancelBtn = document.getElementById('cancel-edit');

    editCloseBtn.addEventListener('click', function() {
        editModal.style.display = 'none';
    });

    editCancelBtn.addEventListener('click', function() {
        editModal.style.display = 'none';
    });

    // Setup add modal close events
    const addModal = document.getElementById('add-modal');
    const addCloseBtn = addModal.querySelector('.close-modal');
    const addCancelBtn = document.getElementById('cancel-add');

    addCloseBtn.addEventListener('click', function() {
        addModal.style.display = 'none';
    });

    addCancelBtn.addEventListener('click', function() {
        addModal.style.display = 'none';
    });

    // Setup form submissions
    document.getElementById('edit-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveDeliveryCost();
    });

    document.getElementById('add-form').addEventListener('submit', function(e) {
        e.preventDefault();
        addNewWilaya();
    });
});

// Load delivery costs
function loadDeliveryCosts() {
    fetch('../backend/get_delivery_costs.php')
        .then(response => response.json())
        .then(data => {
            const tableBody = document.getElementById('delivery-body');
            
            if (!tableBody) return;
            
            if (data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center">No delivery costs found</td></tr>';
                return;
            }
            
            let html = '';
            
            data.forEach(item => {
                html += `
                    <tr>
                        <td>${item.wilaya_code}</td>
                        <td>${item.wilaya_name}</td>
                        <td>
                            <input type="number" class="inline-edit domicile" 
                                data-wilaya="${item.wilaya_code}" 
                                value="${item.domicile}" min="0">
                        </td>
                        <td>
                            <input type="number" class="inline-edit stopdesk" 
                                data-wilaya="${item.wilaya_code}" 
                                value="${item.stopdesk}" min="0">
                        </td>
                        <td>
                            <button class="admin-btn admin-btn-primary btn-sm" onclick="editDeliveryCost('${item.wilaya_code}', '${item.wilaya_name}', ${item.domicile}, ${item.stopdesk})">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                            <button class="admin-btn admin-btn-danger btn-sm" onclick="deleteWilaya('${item.wilaya_code}', '${item.wilaya_name}')" style="margin-left: 5px;">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading delivery costs:', error);
            showNotification('Failed to load delivery costs', 'error');
        });
}

// Edit delivery cost
window.editDeliveryCost = function(wilayaCode, wilayaName, domicile, stopdesk) {
    const modal = document.getElementById('edit-modal');
    
    document.getElementById('edit-wilaya-code').value = wilayaCode;
    document.getElementById('edit-wilaya-name').value = wilayaName;
    document.getElementById('edit-domicile').value = domicile;
    document.getElementById('edit-stopdesk').value = stopdesk;
    
    modal.style.display = 'block';
};

// Save delivery cost
function saveDeliveryCost() {
    const wilayaCode = document.getElementById('edit-wilaya-code').value;
    const domicile = document.getElementById('edit-domicile').value;
    const stopdesk = document.getElementById('edit-stopdesk').value;
    
    const data = {
        wilaya_code: wilayaCode,
        domicile: domicile,
        stopdesk: stopdesk
    };
    
    fetch('../backend/update_delivery_cost.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'Delivery cost updated successfully');
            document.getElementById('edit-modal').style.display = 'none';
            
            // Update the table values
            const domicileInput = document.querySelector(`.domicile[data-wilaya="${wilayaCode}"]`);
            const stopdeskInput = document.querySelector(`.stopdesk[data-wilaya="${wilayaCode}"]`);
            
            if (domicileInput) domicileInput.value = domicile;
            if (stopdeskInput) stopdeskInput.value = stopdesk;
        } else {
            showNotification(data.message || 'Failed to update delivery cost', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating delivery cost:', error);
        showNotification('Failed to update delivery cost', 'error');
    });
}

// Save all changes
function saveAllChanges() {
    const deliveryData = [];
    
    // Get all rows
    const domicileInputs = document.querySelectorAll('.inline-edit.domicile');
    const stopdeskInputs = document.querySelectorAll('.inline-edit.stopdesk');
    
    domicileInputs.forEach((input, index) => {
        const wilayaCode = input.getAttribute('data-wilaya');
        const domicile = input.value;
        const stopdesk = stopdeskInputs[index].value;
        
        deliveryData.push({
            wilaya_code: wilayaCode,
            domicile: domicile,
            stopdesk: stopdesk
        });
    });
    
    fetch('../backend/update_all_delivery_costs.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ delivery_costs: deliveryData })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'All delivery costs updated successfully');
        } else {
            showNotification(data.message || 'Failed to update delivery costs', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating delivery costs:', error);
        showNotification('Failed to update delivery costs', 'error');
    });
}

// Show add wilaya modal
function showAddModal() {
    // Clear form
    document.getElementById('add-form').reset();

    // Show modal
    document.getElementById('add-modal').style.display = 'flex';
}

// Add new wilaya
function addNewWilaya() {
    const form = document.getElementById('add-form');
    const formData = new FormData(form);

    fetch('../backend/add_delivery_cost.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'Wilaya added successfully');

            // Hide modal
            document.getElementById('add-modal').style.display = 'none';

            // Reload delivery costs
            loadDeliveryCosts();
        } else {
            showNotification(data.message || 'Failed to add wilaya', 'error');
        }
    })
    .catch(error => {
        console.error('Error adding wilaya:', error);
        showNotification('Failed to add wilaya', 'error');
    });
}

// Delete wilaya
function deleteWilaya(wilayaCode, wilayaName) {
    if (!confirm(`Are you sure you want to delete "${wilayaName}" (${wilayaCode})?\n\nThis action cannot be undone.`)) {
        return;
    }

    const formData = new FormData();
    formData.append('wilaya_code', wilayaCode);

    fetch('../backend/delete_delivery_cost.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'Wilaya deleted successfully');

            // Reload delivery costs
            loadDeliveryCosts();
        } else {
            showNotification(data.message || 'Failed to delete wilaya', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting wilaya:', error);
        showNotification('Failed to delete wilaya', 'error');
    });
}