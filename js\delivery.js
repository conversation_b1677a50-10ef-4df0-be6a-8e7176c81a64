document.addEventListener('DOMContentLoaded', function() {
    // Load delivery costs
    loadDeliveryCosts();
    
    // Setup event listeners
    document.getElementById('save-all-btn').addEventListener('click', saveAllChanges);
    
    // Setup modal close events
    const modal = document.getElementById('edit-modal');
    const closeBtn = modal.querySelector('.close-modal');
    const cancelBtn = document.getElementById('cancel-edit');
    
    closeBtn.addEventListener('click', function() {
        modal.style.display = 'none';
    });
    
    cancelBtn.addEventListener('click', function() {
        modal.style.display = 'none';
    });
    
    // Setup form submission
    document.getElementById('edit-form').addEventListener('submit', function(e) {
        e.preventDefault();
        saveDeliveryCost();
    });
});

// Load delivery costs
function loadDeliveryCosts() {
    fetch('../backend/get_delivery_costs.php')
        .then(response => response.json())
        .then(data => {
            const tableBody = document.getElementById('delivery-body');
            
            if (!tableBody) return;
            
            if (data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center">No delivery costs found</td></tr>';
                return;
            }
            
            let html = '';
            
            data.forEach(item => {
                html += `
                    <tr>
                        <td>${item.wilaya_code}</td>
                        <td>${item.wilaya_name}</td>
                        <td>
                            <input type="number" class="inline-edit domicile" 
                                data-wilaya="${item.wilaya_code}" 
                                value="${item.domicile}" min="0">
                        </td>
                        <td>
                            <input type="number" class="inline-edit stopdesk" 
                                data-wilaya="${item.wilaya_code}" 
                                value="${item.stopdesk}" min="0">
                        </td>
                        <td>
                            <button class="admin-btn admin-btn-primary btn-sm" onclick="editDeliveryCost('${item.wilaya_code}', '${item.wilaya_name}', ${item.domicile}, ${item.stopdesk})">
                                <i class="fas fa-edit"></i> Edit
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading delivery costs:', error);
            showNotification('Failed to load delivery costs', 'error');
        });
}

// Edit delivery cost
window.editDeliveryCost = function(wilayaCode, wilayaName, domicile, stopdesk) {
    const modal = document.getElementById('edit-modal');
    
    document.getElementById('edit-wilaya-code').value = wilayaCode;
    document.getElementById('edit-wilaya-name').value = wilayaName;
    document.getElementById('edit-domicile').value = domicile;
    document.getElementById('edit-stopdesk').value = stopdesk;
    
    modal.style.display = 'block';
};

// Save delivery cost
function saveDeliveryCost() {
    const wilayaCode = document.getElementById('edit-wilaya-code').value;
    const domicile = document.getElementById('edit-domicile').value;
    const stopdesk = document.getElementById('edit-stopdesk').value;
    
    const data = {
        wilaya_code: wilayaCode,
        domicile: domicile,
        stopdesk: stopdesk
    };
    
    fetch('../backend/update_delivery_cost.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'Delivery cost updated successfully');
            document.getElementById('edit-modal').style.display = 'none';
            
            // Update the table values
            const domicileInput = document.querySelector(`.domicile[data-wilaya="${wilayaCode}"]`);
            const stopdeskInput = document.querySelector(`.stopdesk[data-wilaya="${wilayaCode}"]`);
            
            if (domicileInput) domicileInput.value = domicile;
            if (stopdeskInput) stopdeskInput.value = stopdesk;
        } else {
            showNotification(data.message || 'Failed to update delivery cost', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating delivery cost:', error);
        showNotification('Failed to update delivery cost', 'error');
    });
}

// Save all changes
function saveAllChanges() {
    const deliveryData = [];
    
    // Get all rows
    const domicileInputs = document.querySelectorAll('.inline-edit.domicile');
    const stopdeskInputs = document.querySelectorAll('.inline-edit.stopdesk');
    
    domicileInputs.forEach((input, index) => {
        const wilayaCode = input.getAttribute('data-wilaya');
        const domicile = input.value;
        const stopdesk = stopdeskInputs[index].value;
        
        deliveryData.push({
            wilaya_code: wilayaCode,
            domicile: domicile,
            stopdesk: stopdesk
        });
    });
    
    fetch('../backend/update_all_delivery_costs.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ delivery_costs: deliveryData })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'All delivery costs updated successfully');
        } else {
            showNotification(data.message || 'Failed to update delivery costs', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating delivery costs:', error);
        showNotification('Failed to update delivery costs', 'error');
    });
}