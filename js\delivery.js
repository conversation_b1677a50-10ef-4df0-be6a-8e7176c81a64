document.addEventListener('DOMContentLoaded', function() {
    // Load delivery costs
    loadDeliveryCosts();

    // Setup event listeners
    document.getElementById('save-all-btn').addEventListener('click', saveAllChanges);

    document.getElementById('add-wilaya-btn').addEventListener('click', function() {
        showAddWilayaModal();
    });

    // No edit modal needed anymore

    // Setup add wilaya form submission
    document.getElementById('add-wilaya-form').addEventListener('submit', function(e) {
        e.preventDefault();
        submitNewWilaya();
    });
});

// Load delivery costs
function loadDeliveryCosts() {
    fetch('../backend/get_delivery_costs.php')
        .then(response => response.json())
        .then(data => {
            const tableBody = document.getElementById('delivery-body');
            
            if (!tableBody) return;
            
            if (data.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center">No delivery costs found</td></tr>';
                return;
            }
            
            let html = '';
            
            data.forEach(item => {
                const isActive = item.active == 1;
                const statusClass = isActive ? 'admin-btn-success' : 'admin-btn-secondary';
                const statusIcon = isActive ? 'fas fa-check' : 'fas fa-times';
                const statusText = isActive ? 'Active' : 'Inactive';

                html += `
                    <tr>
                        <td>${item.wilaya_code}</td>
                        <td>${item.wilaya_name}</td>
                        <td>
                            <input type="number" class="inline-edit domicile"
                                data-wilaya="${item.wilaya_code}"
                                value="${item.domicile}" min="0">
                        </td>
                        <td>
                            <input type="number" class="inline-edit stopdesk"
                                data-wilaya="${item.wilaya_code}"
                                value="${item.stopdesk}" min="0">
                        </td>
                        <td>
                            <button class="admin-btn ${statusClass} btn-sm" onclick="toggleWilayaStatus('${item.wilaya_code}', '${item.wilaya_name}')">
                                <i class="${statusIcon}"></i> ${statusText}
                            </button>
                        </td>
                        <td>
                            <button class="admin-btn admin-btn-danger btn-sm" onclick="deleteWilaya('${item.wilaya_code}', '${item.wilaya_name}')">
                                <i class="fas fa-trash"></i> Delete
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading delivery costs:', error);
            showNotification('Failed to load delivery costs', 'error');
        });
}

// Toggle wilaya active status
function toggleWilayaStatus(wilayaCode, wilayaName) {
    if (!confirm(`Are you sure you want to toggle the status of "${wilayaName}" (${wilayaCode})?`)) {
        return;
    }

    const formData = new FormData();
    formData.append('wilaya_code', wilayaCode);

    fetch('../backend/toggle_wilaya_status.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'Wilaya status updated successfully');

            // Reload delivery costs to reflect changes
            loadDeliveryCosts();
        } else {
            showNotification(data.message || 'Failed to update wilaya status', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating wilaya status:', error);
        showNotification('Failed to update wilaya status', 'error');
    });
}

// Make function globally accessible
window.toggleWilayaStatus = toggleWilayaStatus;

// Save all changes
function saveAllChanges() {
    const deliveryData = [];
    
    // Get all rows
    const domicileInputs = document.querySelectorAll('.inline-edit.domicile');
    const stopdeskInputs = document.querySelectorAll('.inline-edit.stopdesk');
    
    domicileInputs.forEach((input, index) => {
        const wilayaCode = input.getAttribute('data-wilaya');
        const domicile = input.value;
        const stopdesk = stopdeskInputs[index].value;
        
        deliveryData.push({
            wilaya_code: wilayaCode,
            domicile: domicile,
            stopdesk: stopdesk
        });
    });
    
    fetch('../backend/update_all_delivery_costs.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({ delivery_costs: deliveryData })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'All delivery costs updated successfully');
        } else {
            showNotification(data.message || 'Failed to update delivery costs', 'error');
        }
    })
    .catch(error => {
        console.error('Error updating delivery costs:', error);
        showNotification('Failed to update delivery costs', 'error');
    });
}

// Show add wilaya modal - Simple version
function showAddWilayaModal() {
    const modal = document.getElementById('add-wilaya-modal');
    if (modal) {
        modal.style.display = 'block';

        // Clear form
        document.getElementById('new-wilaya-code').value = '';
        document.getElementById('new-wilaya-name').value = '';
        document.getElementById('new-domicile').value = '';
        document.getElementById('new-stopdesk').value = '';
    }
}

// Make function globally accessible
window.showAddWilayaModal = showAddWilayaModal;

// Close add wilaya modal
function closeAddModal() {
    const modal = document.getElementById('add-wilaya-modal');
    modal.style.display = 'none';
}

// Make function globally accessible
window.closeAddModal = closeAddModal;

// Submit new wilaya
function submitNewWilaya() {
    const wilayaCode = document.getElementById('new-wilaya-code').value;
    const wilayaName = document.getElementById('new-wilaya-name').value;
    const domicile = document.getElementById('new-domicile').value;
    const stopdesk = document.getElementById('new-stopdesk').value;

    // Basic validation
    if (!wilayaCode || !wilayaName || !domicile || !stopdesk) {
        alert('Please fill in all fields');
        return;
    }

    // Create form data
    const formData = new FormData();
    formData.append('wilaya_code', wilayaCode);
    formData.append('wilaya_name', wilayaName);
    formData.append('domicile', domicile);
    formData.append('stopdesk', stopdesk);

    // Submit to backend
    fetch('../backend/add_delivery_cost.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Wilaya added successfully!');
            closeAddModal();
            loadDeliveryCosts(); // Reload the table
        } else {
            alert('Error: ' + (data.message || 'Failed to add wilaya'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to add wilaya. Please try again.');
    });
}

// Delete wilaya
function deleteWilaya(wilayaCode, wilayaName) {
    if (!confirm(`Are you sure you want to delete "${wilayaName}" (${wilayaCode})?\n\nThis action cannot be undone.`)) {
        return;
    }

    const formData = new FormData();
    formData.append('wilaya_code', wilayaCode);

    fetch('../backend/delete_delivery_cost.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message || 'Wilaya deleted successfully');

            // Reload delivery costs
            loadDeliveryCosts();
        } else {
            showNotification(data.message || 'Failed to delete wilaya', 'error');
        }
    })
    .catch(error => {
        console.error('Error deleting wilaya:', error);
        showNotification('Failed to delete wilaya', 'error');
    });
}