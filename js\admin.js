document.addEventListener('DOMContentLoaded', function() {
    // Check admin login status
    checkLoginStatus();
    
    // Theme toggle functionality for ADMIN dashboard (SEPARATE from client pages)
    const themeToggle = document.getElementById('theme-toggle');
    const body = document.body;

    // Migration: Move old unified 'theme' to 'admin-theme' for separation
    if (localStorage.getItem('theme') && !localStorage.getItem('admin-theme')) {
        localStorage.setItem('admin-theme', localStorage.getItem('theme'));
        localStorage.removeItem('theme'); // Remove unified theme to force separation
    }

    // Check for saved ADMIN theme preference (SEPARATE from client theme)
    if (localStorage.getItem('admin-theme') === 'dark') {
        body.classList.add('dark-mode');
        document.documentElement.classList.add('dark-mode'); // Add dark-mode to html element
        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';

        // Apply dark mode to all admin elements
        applyDarkModeToAdminElements();
    }

    themeToggle.addEventListener('click', function() {
        body.classList.toggle('dark-mode');
        document.documentElement.classList.toggle('dark-mode'); // Toggle dark-mode on html element

        if (body.classList.contains('dark-mode')) {
            localStorage.setItem('admin-theme', 'dark');
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';

            // Apply dark mode to all admin elements
            applyDarkModeToAdminElements();
        } else {
            localStorage.setItem('admin-theme', 'light');
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';

            // Remove dark mode from all admin elements
            removeDarkModeFromAdminElements();
        }
    });
    
    // Logout button
    const logoutBtn = document.getElementById('logout-btn');

    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            
            fetch('../backend/admin_logout.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(data.message || 'Logout successful');
                        
                        // Redirect to login page
                        setTimeout(() => {
                            window.location.href = '../login.html';
                        }, 1000);
                    } else {
                        showNotification(data.message || 'Logout failed', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error logging out:', error);
                    showNotification('Logout failed', 'error');
                });
        });
    }
});

// Apply dark mode to all admin elements
function applyDarkModeToAdminElements() {
    // Apply dark mode to sidebar
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) sidebar.classList.add('dark-mode');
    
    // Apply dark mode to admin-main
    const adminMain = document.querySelector('.admin-main');
    if (adminMain) adminMain.classList.add('dark-mode');
    
    // Apply dark mode to all cards
    const cards = document.querySelectorAll('.stat-card, .admin-card');
    cards.forEach(card => card.classList.add('dark-mode'));
    
    // Apply dark mode to tables
    const tables = document.querySelectorAll('.admin-table');
    tables.forEach(table => table.classList.add('dark-mode'));
    
    // Apply dark mode to forms
    const forms = document.querySelectorAll('.admin-form');
    forms.forEach(form => form.classList.add('dark-mode'));
    
    // Apply dark mode to modals
    const modals = document.querySelectorAll('.modal-content');
    modals.forEach(modal => modal.classList.add('dark-mode'));
    
    // Apply dark mode to buttons
    const buttons = document.querySelectorAll('.admin-btn');
    buttons.forEach(button => {
        if (!button.classList.contains('admin-btn-primary') && 
            !button.classList.contains('admin-btn-danger') && 
            !button.classList.contains('admin-btn-success')) {
            button.classList.add('dark-mode');
        }
    });
}

// Remove dark mode from all admin elements
function removeDarkModeFromAdminElements() {
    // Remove dark mode from sidebar
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) sidebar.classList.remove('dark-mode');
    
    // Remove dark mode from admin-main
    const adminMain = document.querySelector('.admin-main');
    if (adminMain) adminMain.classList.remove('dark-mode');
    
    // Remove dark mode from all cards
    const cards = document.querySelectorAll('.stat-card, .admin-card');
    cards.forEach(card => card.classList.remove('dark-mode'));
    
    // Remove dark mode from tables
    const tables = document.querySelectorAll('.admin-table');
    tables.forEach(table => table.classList.remove('dark-mode'));
    
    // Remove dark mode from forms
    const forms = document.querySelectorAll('.admin-form');
    forms.forEach(form => form.classList.remove('dark-mode'));
    
    // Remove dark mode from modals
    const modals = document.querySelectorAll('.modal-content');
    modals.forEach(modal => modal.classList.remove('dark-mode'));
    
    // Remove dark mode from buttons
    const buttons = document.querySelectorAll('.admin-btn');
    buttons.forEach(button => button.classList.remove('dark-mode'));
}

// Check login status
function checkLoginStatus() {
    fetch('../backend/check_admin_session.php')
        .then(response => response.json())
        .then(data => {
            if (!data.loggedIn) {
                // Redirect to login page
                window.location.href = '../login.html';
            } else {
                // Update admin username
                const adminUsername = document.getElementById('admin-username');
                
                if (adminUsername) {
                    adminUsername.textContent = data.username;
                }
                
                // Apply dark mode if saved (ADMIN theme - SEPARATE from client)
                if (localStorage.getItem('admin-theme') === 'dark') {
                    applyDarkModeToAdminElements();
                }
            }
        })
        .catch(error => {
            console.error('Error checking login status:', error);
            showNotification('Failed to check login status', 'error');
        });
}

// Show notification
function showNotification(message, type = 'success') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        
        // Remove notification from DOM after animation
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Login form
const loginForm = document.getElementById('login-form');

if (loginForm) {
    loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        // Validate form
        if (!username || !password) {
            showNotification('Please fill in all fields', 'error');
            return;
        }
        
        // Create form data
        const formData = new FormData();
        formData.append('username', username);
        formData.append('password', password);
        
        // Submit login
        fetch('backend/admin_login.php', {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification(data.message || 'Login successful');
                    
                    // Redirect to dashboard
                    setTimeout(() => {
                        window.location.href = 'admin/dashboard.html';
                    }, 1000);
                } else {
                    showNotification(data.message || 'Login failed', 'error');
                }
            })
            .catch(error => {
                console.error('Error logging in:', error);
                showNotification('Login failed', 'error');
            });
    });
}



