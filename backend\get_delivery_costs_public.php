<?php
require_once 'config.php';

try {
    // Get only active delivery costs from database ordered by wilaya code numerically
    $stmt = $db->prepare("SELECT wilaya_code, wilaya_name, domicile, stopdesk FROM delivery_costs WHERE active = 1 ORDER BY CAST(wilaya_code AS UNSIGNED) ASC");
    $stmt->execute();
    
    $deliveryCosts = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // If no delivery costs found, return empty array
    if (empty($deliveryCosts)) {
        echo json_encode([]);
        exit;
    }
    
    // Return delivery costs
    echo json_encode($deliveryCosts);
} catch(PDOException $e) {
    // Log error for debugging
    error_log('Error in get_delivery_costs_public.php: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Error fetching delivery costs: ' . $e->getMessage()]);
}


