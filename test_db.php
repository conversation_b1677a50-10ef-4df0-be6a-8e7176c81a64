<?php
// Test database connection
$host = 'localhost';
$dbname = 'algerian_store';
$username = 'root';
$password = '';

try {
    // First, connect without specifying database to check if it exists
    $db = new PDO("mysql:host=$host", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "MySQL connection successful!\n";

    // Check if database exists
    $stmt = $db->query("SHOW DATABASES LIKE '$dbname'");
    if ($stmt->rowCount() == 0) {
        echo "Database '$dbname' does not exist. Available databases:\n";
        $stmt = $db->query("SHOW DATABASES");
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- " . $row['Database'] . "\n";
        }
        exit;
    }

    // Now connect to the specific database
    $db = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $db->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "Database connection successful!\n";
    
    // Test if products table exists
    $stmt = $db->query("SHOW TABLES LIKE 'products'");
    if ($stmt->rowCount() > 0) {
        echo "Products table exists.\n";
        
        // Check if there are any products
        $stmt = $db->query("SELECT COUNT(*) as count FROM products");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "Number of products in database: " . $result['count'] . "\n";
        
        // Show first few products
        $stmt = $db->query("SELECT id, name, category, price FROM products LIMIT 5");
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo "Sample products:\n";
        foreach ($products as $product) {
            echo "- ID: {$product['id']}, Name: {$product['name']}, Category: {$product['category']}, Price: {$product['price']}\n";
        }
    } else {
        echo "Products table does not exist!\n";
    }
    
} catch(PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
}
?>
