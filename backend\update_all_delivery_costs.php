<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Get request body
$data = json_decode(file_get_contents('php://input'), true);

// Validate data
if (!isset($data['delivery_costs']) || !is_array($data['delivery_costs'])) {
    json_response(['success' => false, 'message' => 'Invalid data format']);
}

$deliveryCosts = $data['delivery_costs'];

try {
    // Start transaction
    $db->beginTransaction();
    
    // Prepare statement for updating delivery costs
    $updateStmt = $db->prepare("UPDATE delivery_costs SET domicile = :domicile, stopdesk = :stopdesk WHERE wilaya_code = :code");
    
    foreach ($deliveryCosts as $cost) {
        // Validate required fields
        if (!isset($cost['wilaya_code']) || !isset($cost['domicile']) || !isset($cost['stopdesk'])) {
            continue;
        }
        
        $wilayaCode = sanitize($cost['wilaya_code']);
        $domicile = intval($cost['domicile']);
        $stopdesk = intval($cost['stopdesk']);
        
        // Validate values
        if ($domicile < 0 || $stopdesk < 0) {
            continue;
        }
        
        // Update delivery cost
        $updateStmt->bindParam(':code', $wilayaCode);
        $updateStmt->bindParam(':domicile', $domicile);
        $updateStmt->bindParam(':stopdesk', $stopdesk);
        $updateStmt->execute();
    }
    
    // Commit transaction
    $db->commit();
    
    json_response(['success' => true, 'message' => 'All delivery costs updated successfully']);
} catch(PDOException $e) {
    // Rollback transaction on error
    $db->rollBack();
    json_response(['success' => false, 'message' => 'Error updating delivery costs: ' . $e->getMessage()]);
}