# Finix DZ - E-commerce Website

A professional e-commerce website for Algerian businesses, featuring a modern customer interface and comprehensive admin dashboard for managing products, orders, and delivery costs.

## Features

### Customer Features
- Browse products by category
- Add products to cart
- Checkout process
- Dark/light mode toggle
- Responsive design for all devices

### Admin Features
- Dashboard with sales statistics
- Product management (add, edit, delete)
- Order management
- Account settings

## Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/finix-dz.git
cd finix-dz
```

2. Set up the database:
   - Create a MySQL database named `algerian_store`
   - Import the database schema from `database/schema.sql`

3. Configure the database connection:
   - Open `backend/config.php`
   - Update the database credentials if needed:
     ```php
     $host = 'localhost';
     $dbname = 'algerian_store';
     $username = 'root';
     $password = '';
     ```

4. Set up a web server:
   - Configure your web server (Apache, Nginx, etc.) to serve the project directory
   - Ensure PHP is installed and configured

5. Access the website:
   - Customer interface: `http://localhost/finix-dz/`
   - Admin login: `http://localhost/finix-dz/login.html`
     - Default credentials: 
       - Username: admin
       - Password: admin123

## Directory Structure

- `index.html` - Main customer-facing page
- `checkout.html` - Checkout page
- `confirmation.html` - Order confirmation page
- `login.html` - Admin login page
- `admin/` - Admin panel pages
- `backend/` - PHP backend files
- `css/` - CSS stylesheets
- `js/` - JavaScript files
- `uploads/` - Product images
- `database/` - Database schema

## Technologies Used

- HTML5, CSS3, JavaScript
- PHP with PDO for database operations
- MySQL database
- Font Awesome icons
- Responsive design with CSS Grid and Flexbox

## License

This project is licensed under the MIT License - see the LICENSE file for details.

