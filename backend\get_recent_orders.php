<?php
require_once 'config.php';

// Check admin login
check_admin_login();

try {
    // Get recent orders (limit to 5)
    $stmt = $db->query("SELECT * FROM orders ORDER BY order_date DESC LIMIT 5");
    $orders = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    json_response($orders);
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error fetching recent orders: ' . $e->getMessage()]);
}
