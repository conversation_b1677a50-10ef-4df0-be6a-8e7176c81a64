/* Enhanced root variables for more styling options */
:root {
    --primary-color: #3498db;
    --primary-rgb: 52, 152, 219;
    --secondary-color: #2c3e50;
    --secondary-rgb: 44, 62, 80;
    --success-color: #2ecc71;
    --success-rgb: 46, 204, 113;
    --error-color: #e74c3c;
    --error-rgb: 231, 76, 60;
    --info-color: #3498db;
    --info-rgb: 52, 152, 219;
    --text-color: #333;
    --heading-color: #222;
    --bg-color: #fff;
    --card-bg: #f9f9f9;
    --border-color: #eee;
    
    /* Section background colors */
    --hero-bg: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --products-bg: #f8f9fa;
    --about-bg: #f0f4f8;
    --contact-bg: #f5f8f0;
    --footer-bg: #2c3e50;
}

.dark-mode {
    --text-color: #f0f0f0;
    --heading-color: #ffffff;
    --bg-color: #121212;
    --card-bg: #1e1e1e;
    --border-color: #333;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
    
    /* Section background colors for dark mode */
    --products-bg: #161616;
    --about-bg: #1a1a1a;
    --contact-bg: #181818;
    --footer-bg: #0a0a0a;
    --input-bg: #2c2c2c;
}

/* Global styles enhancement */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--text-color);
    background-color: var(--bg-color);
    transition: var(--transition);
    line-height: 1.6;
}

h1, h2, h3, h4, h5, h6 {
    color: var(--heading-color);
    font-weight: 600;
    line-height: 1.3;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

a:hover {
    color: var(--secondary-color);
}

/* Enhanced header styles */
header {
    background-color: var(--bg-color);
    box-shadow: var(--shadow);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: var(--transition);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
}

/* Enhanced logo styles */
.logo {
    display: flex;
    align-items: center;
}

.logo a, 
.logo h1 {
    color: var(--primary-color);
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: -0.5px;
    text-decoration: none;
    position: relative;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.logo a:hover,
.logo h1:hover {
    color: var(--secondary-color);
    transform: scale(1.02);
}

.logo-icon {
    margin-right: 8px;
    font-size: 1.4em;
    color: var(--primary-color);
}

.logo-text {
    position: relative;
}

.logo-text::after {
    content: 'DZ';
    position: absolute;
    top: -5px;
    right: -20px;
    font-size: 0.5em;
    background-color: var(--primary-color);
    color: white;
    padding: 2px 4px;
    border-radius: 4px;
    font-weight: 700;
}

/* Logo animation */
@keyframes logoGlow {
    0% { text-shadow: 0 0 5px rgba(var(--primary-rgb), 0.3); }
    50% { text-shadow: 0 0 15px rgba(var(--primary-rgb), 0.5); }
    100% { text-shadow: 0 0 5px rgba(var(--primary-rgb), 0.3); }
}

.logo a:hover .logo-text,
.logo h1:hover .logo-text {
    animation: logoGlow 1.5s infinite;
}

/* Dark mode logo adjustments */
.dark-mode .logo a,
.dark-mode .logo h1 {
    color: #4dabf7; /* Brighter blue for dark mode */
}

.dark-mode .logo-text::after {
    background-color: #4dabf7;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    color: var(--text-color);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    padding: 5px 0;
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

nav ul li a:hover::after, 
nav ul li a.active::after {
    width: 100%;
}

nav ul li a:hover, 
nav ul li a.active {
    color: var(--primary-color);
}

.nav-icons {
    display: flex;
    align-items: center;
}

.nav-icons button, .nav-icons a {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    margin-left: 15px;
    cursor: pointer;
    transition: var(--transition);
}

.nav-icons button:hover, .nav-icons a:hover {
    color: var(--primary-color);
}

.cart-icon {
    position: relative;
}

#cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary-color);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background-color: var(--text-color);
    margin: 2px 0;
    transition: var(--transition);
}

/* Theme toggle button */
.theme-toggle {
    background: none;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
    margin-right: 15px;
    transition: transform 0.3s;
}

.theme-toggle:hover {
    transform: rotate(30deg);
}

/* Enhanced Hero section */
.hero {
    background: var(--hero-bg);
    color: white;
    padding: 100px 0;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('../images/hero-pattern.png');
    opacity: 0.1;
    z-index: 1;
}

.hero-content {
    position: relative;
    z-index: 2;
}

.hero-content h2 {
    font-size: 3rem;
    margin-bottom: 20px;
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.btn {
    display: inline-block;
    background-color: white;
    color: var(--primary-color);
    padding: 12px 30px;
    border-radius: 30px;
    text-decoration: none;
    font-weight: bold;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

/* Enhanced Products section */
.products-section {
    background-color: var(--products-bg);
    padding: 80px 0;
    position: relative;
}

.products-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    font-size: 2.5rem;
    color: var(--heading-color);
    position: relative;
    padding-bottom: 15px;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.filter-container {
    display: flex;
    justify-content: center;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.filter-btn {
    background: none;
    border: 2px solid transparent;
    padding: 10px 20px;
    margin: 0 5px 10px;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-color);
    transition: var(--transition);
    border-radius: 30px;
    background-color: var(--card-bg);
    box-shadow: var(--shadow);
}

.filter-btn.active, .filter-btn:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(var(--primary-rgb), 0.2);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 30px;
}

.product-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 30px rgba(0, 0, 0, 0.1);
}

.product-img {
    height: 250px;
    overflow: hidden;
    position: relative;
}

.product-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition);
}

.product-card:hover .product-img img {
    transform: scale(1.1);
}

.product-info {
    padding: 20px;
}

.product-info h3 {
    margin-bottom: 10px;
    font-size: 1.2rem;
}

.product-price {
    color: var(--primary-color);
    font-weight: bold;
    margin-bottom: 15px;
    font-size: 1.3rem;
}

.add-to-cart {
    width: 100%;
    padding: 12px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.add-to-cart i {
    font-size: 1.1rem;
}

.add-to-cart:hover {
    background-color: var(--secondary-color);
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(var(--secondary-rgb), 0.2);
}

/* Checkout page styles */
.checkout-section {
    background-color: var(--products-bg);
    padding: 80px 0;
    position: relative;
}

.checkout-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.checkout-container {
    display: grid;
    grid-template-columns: 1fr 400px;
    gap: 40px;
    align-items: start;
}

.checkout-form-container, .order-summary {
    background-color: var(--card-bg);
    border-radius: 15px;
    padding: 30px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.checkout-form-container h3, .order-summary h3 {
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
    color: var(--heading-color);
    font-size: 1.3rem;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--heading-color);
}

.form-group input, 
.form-group select, 
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    background-color: var(--input-bg);
    color: var(--text-color);
    transition: var(--transition);
}

.form-group input:focus, 
.form-group select:focus, 
.form-group textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.form-group textarea {
    height: 100px;
    resize: vertical;
}

.radio-group {
    display: flex;
    gap: 20px;
}

.radio-label {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
}

.radio-label input {
    width: auto;
}

.cart-items {
    margin-bottom: 20px;
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.cart-item-img {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    overflow: hidden;
}

.cart-item-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 500;
    margin-bottom: 5px;
    color: var(--heading-color);
}

.cart-item-price {
    color: var(--primary-color);
    font-weight: 500;
}

.cart-item-quantity {
    font-size: 0.9rem;
    color: var(--text-color);
    opacity: 0.8;
}

.cart-totals {
    margin-top: 20px;
}

.cart-total-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid var(--border-color);
}

.cart-total-item.total {
    font-weight: bold;
    font-size: 1.2rem;
    color: var(--heading-color);
    border-bottom: none;
    padding-top: 15px;
}

/* Confirmation page styles */
.confirmation-page {
    display: flex;
    flex-direction: column;
    min-height: 60vh;
}

.confirmation {
    background-color: var(--products-bg);
    padding: 80px 0;
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.confirmation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.confirmation .container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: center;
}

.confirmation-card {
    background-color: var(--card-bg);
    border-radius: 15px;
    padding: 40px;
    text-align: center;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
    max-width: 450px;
    width: 100%;
}

.confirmation-icon {
    font-size: 3rem;
    color: var(--success-color);
    margin-bottom: 20px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
    background-color: rgba(var(--success-rgb), 0.1);
    border-radius: 50%;
}

.confirmation-card h2 {
    margin-bottom: 15px;
    color: var(--heading-color);
    font-size: 1.6rem;
}

.confirmation-card p {
    margin-bottom: 15px;
    color: var(--text-color);
    font-size: 0.95rem;
}

.order-info {
    background-color: rgba(var(--primary-rgb), 0.1);
    padding: 12px;
    border-radius: 8px;
    margin: 15px auto;
    display: inline-block;
    min-width: 180px;
}

.order-info p {
    margin: 0;
    font-weight: 500;
    font-size: 0.9rem;
}

.order-info span {
    font-weight: bold;
    color: var(--primary-color);
}

.confirmation-card .btn {
    margin-top: 15px;
    display: inline-block;
}

/* Media queries for confirmation page */
@media (max-width: 576px) {
    .confirmation-card {
        padding: 30px 20px;
        max-width: 90%;
    }
    
    .confirmation-icon {
        width: 70px;
        height: 70px;
        font-size: 2.5rem;
    }
    
    .confirmation-card h2 {
        font-size: 1.4rem;
    }
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 8px;
    background-color: #4CAF50;
    color: white;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    transform: translateY(-100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    font-weight: 500;
    font-size: 16px;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification.error {
    background-color: #F44336;
}

.notification.info {
    background-color: #2196F3;
}

/* Enhanced About section */
.about-contact {
    position: relative;
}

.about-section {
    background-color: var(--about-bg);
    padding: 80px 0;
    position: relative;
}

.about-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg, var(--secondary-color), var(--primary-color));
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: center;
}

.about-image {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.about-image::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    width: 70px;
    height: 70px;
    border-top: 5px solid var(--primary-color);
    border-left: 5px solid var(--primary-color);
    z-index: 1;
}

.about-image::after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: -10px;
    width: 70px;
    height: 70px;
    border-bottom: 5px solid var(--secondary-color);
    border-right: 5px solid var(--secondary-color);
    z-index: 1;
}

.about-image img {
    width: 100%;
    border-radius: 15px;
    position: relative;
    z-index: 2;
}

.about-text {
    padding: 20px;
}

.about-text p {
    margin-bottom: 20px;
    line-height: 1.8;
}

/* Enhanced Contact section */
.contact-section {
    background-color: var(--contact-bg);
    padding: 80px 0;
    position: relative;
}

.contact-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 10px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 25px;
    background-color: var(--card-bg);
    border-radius: 15px;
    box-shadow: var(--shadow);
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.contact-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.contact-item i {
    font-size: 2rem;
    color: var(--primary-color);
    background-color: rgba(var(--primary-rgb), 0.1);
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.contact-item h3 {
    margin-bottom: 8px;
    font-size: 1.2rem;
    color: var(--heading-color);
}

.contact-item p {
    color: var(--text-color);
    line-height: 1.6;
}

.contact-map {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.contact-map img {
    width: 100%;
    height: auto;
    display: block;
}

.map-caption {
    text-align: center;
    padding: 15px;
    background-color: var(--card-bg);
    color: var(--text-color);
    font-style: italic;
    border-top: 1px solid var(--border-color);
}

/* Enhanced Footer */
footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 80px 0 20px;
    position: relative;
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 40px;
    margin-bottom: 40px;
}

.footer-logo h2 {
    color: var(--primary-color);
    margin-bottom: 15px;
    font-size: 1.8rem;
}

.footer-logo p {
    opacity: 0.8;
    line-height: 1.6;
}

.footer-links h3,
.footer-contact h3,
.footer-social h3 {
    margin-bottom: 25px;
    font-size: 1.3rem;
    color: var(--primary-color);
    position: relative;
    padding-bottom: 10px;
}

.footer-links h3::after,
.footer-contact h3::after,
.footer-social h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-links ul {
    list-style: none;
}

.footer-links ul li {
    margin-bottom: 12px;
}

.footer-links ul li a {
    color: var(--footer-text);
    text-decoration: none;
    transition: var(--transition);
    opacity: 0.8;
    display: inline-block;
}

.footer-links ul li a:hover {
    color: var(--primary-color);
    opacity: 1;
    transform: translateX(5px);
}

.footer-contact p {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    opacity: 0.8;
}

.footer-contact p i {
    color: var(--primary-color);
    font-size: 1.2rem;
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--footer-text);
    transition: var(--transition);
    font-size: 1.2rem;
}

.social-icons a:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-5px);
}

.footer-bottom {
    text-align: center;
    padding-top: 25px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Responsive styles for contact section */
@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
    }
    
    .contact-map {
        order: -1;
    }
}

/* Footer Styles */
footer {
    background-color: var(--footer-bg);
    color: var(--footer-text);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-bottom: 30px;
}

.footer-logo h2 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.footer-logo p {
    opacity: 0.8;
}

.footer-links h3,
.footer-contact h3,
.footer-social h3 {
    margin-bottom: 20px;
    font-size: 1.2rem;
    color: var(--primary-color);
}

.footer-links ul {
    list-style: none;
}

.footer-links ul li {
    margin-bottom: 10px;
}

.footer-links ul li a {
    color: var(--footer-text);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links ul li a:hover {
    color: var(--primary-color);
}

.footer-contact p {
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.footer-contact p i {
    color: var(--primary-color);
}

.social-icons {
    display: flex;
    gap: 15px;
}

.social-icons a {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    color: var(--footer-text);
    transition: var(--transition);
}

.social-icons a:hover {
    background-color: var(--primary-color);
    color: white;
}

.footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Responsive styles for About + Contact section */
@media (max-width: 992px) {
    .about-contact-container {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .about-content, .contact-content {
        grid-template-columns: 1fr;
    }
    
    .footer-content {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 576px) {
    .footer-content {
        grid-template-columns: 1fr;
    }
}

/* Login page styles */
.login-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.login-form {
    background-color: var(--bg-color);
    padding: 40px;
    border-radius: 10px;
    box-shadow: var(--shadow);
    width: 100%;
    max-width: 400px;
}

.login-form .logo {
    text-align: center;
    margin-bottom: 30px;
}

.login-form .logo h1 {
    margin-bottom: 5px;
}

.login-form .logo p {
    color: var(--text-color);
    opacity: 0.8;
}

#login-message {
    margin-top: 15px;
    text-align: center;
    color: #e74c3c;
}

.login-form .theme-toggle {
    margin-top: 20px;
    text-align: center;
}

/* Responsive styles */
@media (max-width: 992px) {
    .checkout-container {
        grid-template-columns: 1fr;
    }
    
    .order-summary {
        order: 2;
    }
    
    .customer-info {
        order: 1;
    }
}

@media (max-width: 768px) {
    nav ul {
        display: none;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--bg-color);
        flex-direction: column;
        padding: 20px;
        box-shadow: var(--shadow);
    }
    
    nav ul.active {
        display: flex;
    }
    
    nav ul li {
        margin: 10px 0;
    }
    
    .hamburger {
        display: flex;
    }
    
    .hero-content h2 {
        font-size: 2rem;
    }
    
    .hero-content p {
        font-size: 1rem;
    }
}

@media (max-width: 576px) {
    .products-grid {
        grid-template-columns: 1fr;
    }
    
    .section-title {
        font-size: 1.8rem;
    }
    
    .hero {
        padding: 60px 0;
    }
    
    .hero-content h2 {
        font-size: 1.8rem;
    }
}

/* Container query implementation */
.product-card {
    container-type: inline-size;
    container-name: card;
}

@container card (min-width: 300px) {
    .product-info h3 {
        font-size: 1.3rem;
    }
    
    .product-price {
        font-size: 1.2rem;
    }
}

@container card (max-width: 299px) {
    .product-info h3 {
        font-size: 1rem;
    }
    
    .product-price {
        font-size: 0.9rem;
    }
}

/* Enhanced Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: white;
    color: #333;
    padding: 10px 15px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    transform: translateX(120%);
    transition: transform 0.3s ease;
    max-width: 250px;
    border-left: 3px solid var(--primary-color);
    font-weight: 500;
    font-size: 0.9rem;
}

.notification.show {
    transform: translateX(0);
}

.notification.success {
    border-left-color: var(--success-color);
}

.notification.error {
    border-left-color: var(--error-color);
}

.notification.info {
    border-left-color: var(--info-color);
}

.notification-icon {
    margin-right: 12px;
    font-size: 1.5rem;
}

.notification.success .notification-icon {
    color: #4CAF50;
}

.notification.error .notification-icon {
    color: #F44336;
}

.notification.info .notification-icon {
    color: #2196F3;
}

.notification-message {
    flex: 1;
    font-size: 0.95rem;
}

/* Cart Overlay */
.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.cart-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Cart Sidebar */
.cart-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 380px;
    height: 100vh;
    background-color: #fff;
    box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
    z-index: 1001;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.dark-mode .cart-sidebar {
    background-color: #333;
    color: #fff;
}

.cart-sidebar.active {
    right: 0;
}

.cart-sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #eee;
}

.dark-mode .cart-sidebar-header {
    border-bottom: 1px solid #444;
}

.cart-sidebar-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

.close-cart {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #333;
}

.dark-mode .close-cart {
    color: #fff;
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.cart-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
    position: relative;
}

.dark-mode .cart-item {
    border-bottom: 1px solid #444;
}

.cart-item-img {
    width: 80px;
    height: 80px;
    margin-right: 15px;
}

.cart-item-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 4px;
}

.cart-item-details {
    flex: 1;
}

.cart-item-details h4 {
    margin: 0 0 5px;
    font-size: 1rem;
}

.cart-item-price {
    font-weight: bold;
    margin-bottom: 5px;
    color: #4CAF50;
}

.dark-mode .cart-item-price {
    color: #8BC34A;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
}

.quantity-btn {
    width: 25px;
    height: 25px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
}

.dark-mode .quantity-btn {
    background-color: #444;
    border: 1px solid #555;
    color: #fff;
}

.cart-item-quantity span {
    margin: 0 10px;
}

.remove-item {
    position: absolute;
    top: 0;
    right: 0;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #333;
}

.dark-mode .remove-item {
    color: #fff;
}

.cart-sidebar-footer {
    padding: 20px;
    border-top: 1px solid #eee;
}

.dark-mode .cart-sidebar-footer {
    border-top: 1px solid #444;
}

.cart-total {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-weight: bold;
}

.checkout-btn {
    display: block;
    width: 100%;
    padding: 12px;
    background-color: #4CAF50;
    color: white;
    text-align: center;
    border-radius: 4px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
}

.checkout-btn:hover {
    background-color: #45a049;
}

.empty-cart {
    text-align: center;
    padding: 20px;
    color: #999;
}

.dark-mode .empty-cart {
    color: #aaa;
}

/* Cart count badge */
.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary-color);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Cart icon in header */
.cart-icon {
    position: relative;
    display: inline-block;
    margin-left: 15px;
    font-size: 1.2rem;
    color: var(--text-color);
    text-decoration: none;
}

/* Responsive styles for cart sidebar */
@media (max-width: 480px) {
    .cart-sidebar {
        width: 100%;
    }
}

/* Mobile navigation styles */
@media (max-width: 768px) {
    nav {
        position: fixed;
        top: 0;
        right: -100%;
        width: 80%;
        height: 100vh;
        background-color: var(--bg-color);
        box-shadow: -5px 0 15px rgba(0, 0, 0, 0.1);
        transition: right 0.3s ease;
        z-index: 1000;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    nav.active {
        right: 0;
    }
    
    nav ul {
        flex-direction: column;
        align-items: center;
    }
    
    nav ul li {
        margin: 15px 0;
    }
    
    .hamburger {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 30px;
        height: 20px;
        cursor: pointer;
        z-index: 1001;
    }
    
    .hamburger span {
        display: block;
        width: 100%;
        height: 2px;
        background-color: var(--text-color);
        transition: var(--transition);
    }
    
    .hamburger.active span:nth-child(1) {
        transform: translateY(9px) rotate(45deg);
    }
    
    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }
    
    .hamburger.active span:nth-child(3) {
        transform: translateY(-9px) rotate(-45deg);
    }
}

/* Admin panel styles */
.admin-container {
    display: grid;
    grid-template-columns: 250px 1fr;
    min-height: 100vh;
}

.admin-sidebar {
    background-color: var(--card-bg);
    border-right: 1px solid var(--border-color);
    padding: 30px 0;
    position: sticky;
    top: 0;
    height: 100vh;
    overflow-y: auto;
}

.admin-logo {
    padding: 0 20px 20px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.admin-logo h2 {
    color: var(--primary-color);
    font-size: 1.5rem;
}

.admin-nav ul {
    list-style: none;
}

.admin-nav ul li {
    margin-bottom: 5px;
}

.admin-nav ul li a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-color);
    text-decoration: none;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.admin-nav ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.admin-nav ul li a:hover,
.admin-nav ul li a.active {
    background-color: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.admin-content {
    padding: 30px;
    background-color: var(--bg-color);
}

.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--border-color);
}

.admin-title {
    font-size: 1.8rem;
    color: var(--heading-color);
}

.admin-user {
    display: flex;
    align-items: center;
    gap: 15px;
}

.admin-user-info {
    text-align: right;
}

.admin-user-name {
    font-weight: 500;
    color: var(--heading-color);
}

.admin-user-role {
    font-size: 0.8rem;
    color: var(--text-color);
    opacity: 0.7;
}

.admin-user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 20px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border-color);
}

.stat-card-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;
    font-size: 1.5rem;
    color: white;
}

.stat-card:nth-child(1) .stat-card-icon {
    background-color: var(--primary-color);
}

.stat-card:nth-child(2) .stat-card-icon {
    background-color: var(--info-color);
}

.stat-card:nth-child(3) .stat-card-icon {
    background-color: var(--warning-color);
}

.stat-card:nth-child(4) .stat-card-icon {
    background-color: var(--success-color);
}

.stat-card-value {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--heading-color);
    margin-bottom: 5px;
}

.stat-card-label {
    color: var(--text-color);
    opacity: 0.7;
}

.admin-card {
    background-color: var(--card-bg);
    border-radius: 10px;
    padding: 25px;
    box-shadow: var(--shadow);
    margin-bottom: 30px;
    border: 1px solid var(--border-color);
}

.admin-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid var(--border-color);
}

.admin-card-title {
    font-size: 1.3rem;
    color: var(--heading-color);
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
}

.admin-table th,
.admin-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

.admin-table th {
    font-weight: 600;
    color: var(--heading-color);
    background-color: rgba(var(--primary-rgb), 0.05);
}

.admin-table tr:last-child td {
    border-bottom: none;
}

.admin-table tr:hover td {
    background-color: rgba(var(--primary-rgb), 0.03);
}

.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-pending {
    background-color: rgba(var(--warning-rgb), 0.1);
    color: var(--warning-color);
}

.status-completed {
    background-color: rgba(var(--success-rgb), 0.1);
    color: var(--success-color);
}

.status-cancelled {
    background-color: rgba(var(--error-rgb), 0.1);
    color: var(--error-color);
}

.action-buttons {
    display: flex;
    gap: 10px;
}

.btn-icon {
    width: 35px;
    height: 35px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    border: none;
    cursor: pointer;
    transition: var(--transition);
}

.btn-edit {
    background-color: var(--info-color);
}

.btn-delete {
    background-color: var(--error-color);
}

.btn-view {
    background-color: var(--primary-color);
}

.btn-icon:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

/* Form styles for admin */
.admin-form {
    max-width: 800px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
}

.btn {
    padding: 12px 25px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-secondary {
    background-color: var(--secondary-color);
    color: white;
}

.btn-success {
    background-color: var(--success-color);
    color: white;
}

.btn-danger {
    background-color: var(--error-color);
    color: white;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
}

/* Responsive admin panel */
@media (max-width: 992px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .admin-container {
        grid-template-columns: 1fr;
    }
    
    .admin-sidebar {
        position: fixed;
        left: -100%;
        width: 250px;
        z-index: 100;
        transition: left 0.3s ease;
    }
    
    .admin-sidebar.active {
        left: 0;
    }
    
    .admin-toggle {
        display: block;
        position: fixed;
        bottom: 20px;
        right: 20px;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: var(--primary-color);
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        z-index: 101;
        cursor: pointer;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 576px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .admin-content {
        padding: 20px;
    }
    
    .admin-table {
        display: block;
        overflow-x: auto;
    }
}

/* Order Summary Styles */
.order-summary img,
#cart-items img,
.summary-items img {
    max-width: 70px;
    max-height: 70px;
    object-fit: cover;
    border-radius: 8px;
}

.checkout-item {
    display: flex;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
}

.checkout-item-img {
    width: 70px;
    height: 70px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
    margin-right: 15px;
}

.checkout-item-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Fix for large images in checkout page */
.summary-items .checkout-item img,
#cart-items .checkout-item img {
    width: 70px !important;
    height: 70px !important;
    object-fit: cover !important;
}

/* Form validation styles */
.error-field {
    border: 2px solid #ff3333 !important;
    background-color: rgba(255, 51, 51, 0.05);
}

.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    background-color: #4CAF50;
    color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    z-index: 10000;
    transform: translateY(-100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    font-weight: 500;
    font-size: 16px;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification.error {
    background-color: #F44336;
}

.notification.info {
    background-color: #2196F3;
}

/* Cart icon styles - improved */
.cart-icon {
    position: relative;
    display: inline-block;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s;
    z-index: 100;
    margin-left: 15px;
}

.cart-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.cart-icon i {
    font-size: 1.4rem;
    color: var(--text-color);
    pointer-events: none; /* Ensure clicks go to the parent element */
}

/* Cart count badge */
.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary-color);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    pointer-events: none; /* Ensure clicks go to the parent element */
}

/* Notification styles - ensure it doesn't overlap with cart */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 10px 15px;
    border-radius: 4px;
    background-color: var(--success-color);
    color: white;
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
    transform: translateY(-100px);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
    z-index: 999; /* Below cart sidebar but above other elements */
    max-width: 250px;
    font-size: 14px;
}

.notification.show {
    transform: translateY(0);
    opacity: 1;
}

.notification.error {
    background-color: var(--error-color);
}

.notification.info {
    background-color: var(--info-color);
}

/* Product not found message */
.product-not-found {
    display: none;
    background-color: #f44336;
    color: white;
    padding: 15px;
    border-radius: 4px;
    margin: 20px 0;
    text-align: center;
}

/* Cart icon styles */
.cart-icon {
    position: relative;
    display: inline-block;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    transition: background-color 0.3s;
}

.cart-icon:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.cart-icon i {
    font-size: 1.4rem;
    color: var(--text-color);
}

/* Cart count badge */
.cart-count {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: var(--primary-color);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

/* Cart item styles */
.cart-item {
    display: flex;
    padding: 15px 0;
    border-bottom: 1px solid var(--border-color);
    position: relative;
}

.cart-item-img {
    width: 70px;
    height: 70px;
    margin-right: 15px;
    border-radius: 4px;
    overflow: hidden;
}

.cart-item-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-info h4 {
    margin: 0 0 5px;
    font-size: 1rem;
}

.cart-item-price {
    font-size: 0.9rem;
    color: var(--text-color-light);
    margin-bottom: 5px;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    margin: 8px 0;
}

.quantity-btn {
    width: 25px;
    height: 25px;
    background-color: var(--bg-color-light);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}

.quantity-btn:hover {
    background-color: var(--primary-color-light);
}

.quantity {
    margin: 0 10px;
    font-weight: 500;
}

.cart-item-total {
    font-weight: 600;
    margin-top: 5px;
}

.remove-item {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1rem;
    padding: 5px;
    transition: color 0.2s;
    position: absolute;
    top: 15px;
    right: 0;
}

.remove-item:hover {
    color: var(--error-color);
}

/* Dark mode styles for cart items */
.dark-mode .cart-item {
    border-color: var(--border-color);
}

.dark-mode .quantity-btn {
    background-color: #333;
    border-color: #444;
    color: var(--text-color);
}

.dark-mode .quantity-btn:hover {
    background-color: #444;
}

.dark-mode .remove-item {
    color: #777;
}

.dark-mode .remove-item:hover {
    color: var(--error-color);
}

/* Fix notification size */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 8px 12px;
    border-radius: 4px;
    background-color: #4CAF50;
    color: white;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 999;
    max-width: 200px;
    font-size: 14px;
}

/* Fix product not found message */
#product-not-found {
    background-color: #f44336;
    color: white;
    padding: 10px;
    border-radius: 4px;
    margin: 10px;
    text-align: center;
    position: absolute;
    top: 70px;
    right: 10px;
    width: auto;
    max-width: 200px;
    font-size: 14px;
    z-index: 100;
}







