document.addEventListener('DOMContentLoaded', function() {
    // Load dashboard stats
    loadDashboardStats();
    
    // Load recent orders
    loadRecentOrders();
});

// Load dashboard stats
function loadDashboardStats() {
    fetch('../backend/get_dashboard_stats.php')
        .then(response => response.json())
        .then(data => {
            document.getElementById('total-orders').textContent = data.totalOrders;
            document.getElementById('total-products').textContent = data.totalProducts;
            document.getElementById('pending-orders').textContent = data.pendingOrders;
            document.getElementById('total-revenue').textContent = data.totalRevenue + ' DZD';
        })
        .catch(error => {
            console.error('Error loading dashboard stats:', error);
            showNotification('Failed to load dashboard stats', 'error');
        });
}

// Load recent orders
function loadRecentOrders() {
    fetch('../backend/get_recent_orders.php')
        .then(response => response.json())
        .then(orders => {
            const tableBody = document.getElementById('recent-orders-body');
            
            if (!tableBody) return;
            
            if (orders.length === 0) {
                tableBody.innerHTML = '<tr><td colspan="5" class="text-center">No orders found</td></tr>';
                return;
            }
            
            let html = '';
            
            orders.forEach(order => {
                html += `
                    <tr>
                        <td>#${order.id}</td>
                        <td>${order.customer_name}</td>
                        <td>${order.total_amount} DZD</td>
                        <td><span class="status-badge status-${order.status.toLowerCase()}">${order.status}</span></td>
                        <td>${formatDate(order.order_date)}</td>
                    </tr>
                `;
            });
            
            tableBody.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading recent orders:', error);
            showNotification('Failed to load recent orders', 'error');
        });
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}





