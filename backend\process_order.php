<?php
require_once 'config.php';

// Validate form data
if (!isset($_POST['name']) || !isset($_POST['phone']) || !isset($_POST['address']) || !isset($_POST['wilaya']) || !isset($_POST['delivery_type']) || !isset($_POST['cart'])) {
    json_response(['success' => false, 'message' => 'Missing required fields']);
}

$name = sanitize($_POST['name']);
$phone = sanitize($_POST['phone']);
$address = sanitize($_POST['address']);
$wilaya = sanitize($_POST['wilaya']);
$deliveryType = sanitize($_POST['delivery_type']);
$notes = isset($_POST['notes']) ? sanitize($_POST['notes']) : '';
$cart = json_decode($_POST['cart'], true);

// Validate cart
if (empty($cart)) {
    json_response(['success' => false, 'message' => 'Cart is empty']);
}

try {
    // Start transaction
    $db->beginTransaction();
    
    // Insert order
    $stmt = $db->prepare("INSERT INTO orders (customer_name, customer_phone, customer_address, customer_city, notes, status, order_date) VALUES (:name, :phone, :address, :city, :notes, 'Pending', NOW())");
    $stmt->bindParam(':name', $name);
    $stmt->bindParam(':phone', $phone);
    $stmt->bindParam(':address', $address);
    $stmt->bindParam(':city', $wilaya); // Bind wilaya to city parameter
    $stmt->bindParam(':notes', $notes);
    $stmt->execute();
    
    $orderId = $db->lastInsertId();
    
    // Calculate total amount
    $totalAmount = 0;
    
    // Get shipping cost based on wilaya and delivery type
    $shipping = 0;
    try {
        $stmt = $db->prepare("SELECT * FROM delivery_costs WHERE wilaya_code = :code");
        $stmt->bindParam(':code', $wilaya);
        $stmt->execute();
        
        $deliveryCost = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($deliveryCost) {
            if ($deliveryType === 'domicile') {
                $shipping = $deliveryCost['domicile'];
            } else if ($deliveryType === 'stopdesk') {
                $shipping = $deliveryCost['stopdesk'];
            }
        } else {
            // Default shipping costs if not found
            $shipping = ($deliveryType === 'domicile') ? 1000 : 700;
        }
    } catch(PDOException $e) {
        // Default shipping costs on error
        $shipping = ($deliveryType === 'domicile') ? 1000 : 700;
    }
    
    // Insert order items
    $stmt = $db->prepare("INSERT INTO order_items (order_id, product_id, quantity, price) VALUES (:order_id, :product_id, :quantity, :price)");
    
    foreach ($cart as $item) {
        $productId = $item['id'];
        $quantity = $item['quantity'];
        $price = $item['price'];
        
        $stmt->bindParam(':order_id', $orderId);
        $stmt->bindParam(':product_id', $productId);
        $stmt->bindParam(':quantity', $quantity);
        $stmt->bindParam(':price', $price);
        $stmt->execute();
        
        // Update product stock
        $updateStmt = $db->prepare("UPDATE products SET stock = stock - :quantity WHERE id = :id");
        $updateStmt->bindParam(':quantity', $quantity);
        $updateStmt->bindParam(':id', $productId);
        $updateStmt->execute();
        
        // Calculate item total
        $totalAmount += $price * $quantity;
    }
    
    // Add shipping fee
    $totalAmount += $shipping;
    
    // Update order total
    $stmt = $db->prepare("UPDATE orders SET total_amount = :total WHERE id = :id");
    $stmt->bindParam(':total', $totalAmount);
    $stmt->bindParam(':id', $orderId);
    $stmt->execute();
    
    // Commit transaction
    $db->commit();
    
    // Return success response
    json_response(['success' => true, 'message' => 'Order placed successfully', 'order_id' => $orderId]);
} catch(PDOException $e) {
    // Rollback transaction on error
    $db->rollBack();
    json_response(['success' => false, 'message' => 'Error processing order: ' . $e->getMessage()]);
}





