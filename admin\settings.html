<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Glam Algeria Admin</title>
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="../css/admin.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="admin-page">
    <div class="admin-container">
        <aside class="sidebar">
            <div class="logo">
                <h2>Glam Admin</h2>
            </div>
            <nav class="admin-nav">
                <ul>
                    <li><a href="dashboard.html"><i class="fas fa-tachometer-alt"></i> Dashboard</a></li>
                    <li><a href="products.html"><i class="fas fa-box"></i> Products</a></li>
                    <li><a href="orders.html"><i class="fas fa-shopping-cart"></i> Orders</a></li>
                    <li><a href="delivery.html"><i class="fas fa-truck"></i> Delivery</a></li>
                    <li><a href="settings.html" class="active"><i class="fas fa-cog"></i> Settings</a></li>
                    <li><a href="#" id="logout-btn"><i class="fas fa-sign-out-alt"></i> Logout</a></li>
                </ul>
            </nav>
        </aside>
        
        <main class="admin-main">
            <header class="admin-header">
                <h1>Settings</h1>
                <div class="admin-header-right">
                    <button id="theme-toggle"><i class="fas fa-moon"></i></button>
                    <div class="admin-user">
                        <span id="admin-username">Admin</span>
                    </div>
                </div>
            </header>
            
            <div class="admin-form">
                <h2>Account Settings</h2>
                <form id="settings-form">
                    <div class="form-group">
                        <label for="username">Username</label>
                        <input type="text" id="username" name="username" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="current-password">Current Password</label>
                        <input type="password" id="current-password" name="current_password">
                        <small>Leave empty if you don't want to change your password</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="new-password">New Password</label>
                        <input type="password" id="new-password" name="new_password">
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm-password">Confirm New Password</label>
                        <input type="password" id="confirm-password" name="confirm_password">
                    </div>
                    
                    <div class="form-buttons">
                        <button type="submit" class="admin-btn admin-btn-primary">Save Settings</button>
                    </div>
                </form>
            </div>
        </main>
    </div>
    
    <script src="../js/admin.js"></script>
    <script src="../js/settings.js"></script>
</body>
</html>

