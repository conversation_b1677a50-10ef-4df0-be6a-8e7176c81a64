// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');

    // Theme toggle functionality
    setupThemeToggle();

    // Load products
    setupProductFiltering();
});

/**
 * Theme System:
 * - Client pages (index.html, checkout.html, etc.) use 'client-theme' localStorage key
 * - Admin dashboard pages use 'admin-theme' localStorage key
 * - This allows SEPARATE dark mode preferences for customers and admins
 */

// Setup theme toggle
function setupThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    console.log('Theme toggle element:', themeToggle);

    if (!themeToggle) {
        console.error('Theme toggle button not found');
        return;
    }

    const body = document.body;

    // Migration: Move old unified 'theme' to 'client-theme' for separation
    if (localStorage.getItem('theme') && !localStorage.getItem('client-theme')) {
        localStorage.setItem('client-theme', localStorage.getItem('theme'));
        localStorage.removeItem('theme'); // Remove unified theme to force separation
    }

    // Check for saved CLIENT theme preference (SEPARATE from admin theme)
    if (localStorage.getItem('client-theme') === 'dark') {
        body.classList.add('dark-mode');
        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
    }

    // Add click event listener
    themeToggle.addEventListener('click', function() {
        console.log('Theme toggle clicked');

        // Toggle dark mode class
        body.classList.toggle('dark-mode');

        // Update icon and save CLIENT theme preference (SEPARATE from admin)
        if (body.classList.contains('dark-mode')) {
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            localStorage.setItem('client-theme', 'dark');
        } else {
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            localStorage.setItem('client-theme', 'light');
        }
    });
}

// Setup product filtering
function setupProductFiltering() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const productsContainer = document.querySelector('.products-grid');
    
    if (filterBtns.length > 0 && productsContainer) {
        let category = 'all';
        
        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                filterBtns.forEach(btn => btn.classList.remove('active'));
                
                // Add active class to clicked button
                this.classList.add('active');
                
                // Get category
                category = this.getAttribute('data-filter');
                
                // Load products
                loadProducts(category);
            });
        });
        
        // Load initial products
        loadProducts(category);
    } else {
        console.error('Filter buttons or products container not found');
    }
}

// Load products function
function loadProducts(category = 'all') {
    console.log('Loading products for category:', category);
    const productsGrid = document.querySelector('.products-grid');
    
    if (!productsGrid) {
        console.error('Products grid not found');
        return;
    }
    
    // Show loading indicator
    productsGrid.innerHTML = '<div class="loading">Loading products...</div>';
    
    // Fetch products from backend
    fetch(`backend/get_products.php${category !== 'all' ? '?category=' + category : ''}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(products => {
            console.log('Products loaded:', products);
            
            if (!products || products.length === 0) {
                productsGrid.innerHTML = '<div class="no-products">No products found in this category</div>';
                return;
            }
            
            // Generate products HTML
            let html = '';
            
            products.forEach(product => {
                html += `
                    <div class="product-card" onclick="viewProduct(${product.id})" style="cursor: pointer;">
                        <div class="product-img">
                            <img src="${product.image}" alt="${product.name}">
                        </div>
                        <div class="product-info">
                            <h3>${product.name}</h3>
                            <div class="product-price">${product.price} DZD</div>
                            <button class="add-to-cart" onclick="event.stopPropagation(); addToCart(${product.id}, '${product.name}', ${product.price}, '${product.image}')">
                                <i class="fas fa-shopping-cart"></i> Add to Cart
                            </button>
                        </div>
                    </div>
                `;
            });
            
            productsGrid.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading products:', error);
            productsGrid.innerHTML = '<div class="error">Failed to load products. Please try again later.</div>';
        });
}

// View product details
function viewProduct(productId) {
    // Navigate to product detail page with product ID
    window.location.href = `product-detail.html?id=${productId}`;
}

// Add to cart function with direct parameters
function addToCart(productId, productName, productPrice, productImage) {
    console.log('Adding product to cart:', productId, productName, productPrice, productImage);
    
    // Get current cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Check if product already in cart
    const existingProductIndex = cart.findIndex(item => item.id === productId);
    
    if (existingProductIndex !== -1) {
        // Product already in cart, increase quantity
        cart[existingProductIndex].quantity += 1;
        showNotification('Product quantity updated in cart', 'success');
    } else {
        // Add product to cart
        cart.push({
            id: productId,
            name: productName,
            price: productPrice,
            image: productImage,
            quantity: 1
        });
        showNotification('Product added to cart', 'success');
    }

    // Save cart
    localStorage.setItem('cart', JSON.stringify(cart));
    
    // Update cart count
    updateCartCount();
}

// Update cart count
function updateCartCount() {
    const cartCount = document.querySelector('.cart-count');

    if (!cartCount) return;

    const cart = JSON.parse(localStorage.getItem('cart')) || [];

    // Count total quantity of all items
    const totalQuantity = cart.reduce((total, item) => total + (item.quantity || 1), 0);

    cartCount.textContent = totalQuantity;
}

// Show notification
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to body
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        
        // Remove from DOM after animation
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Remove from cart
function removeFromCart(productId) {
    console.log('Removing product from cart:', productId);
    
    // Get current cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Remove product from cart
    cart = cart.filter(item => item.id !== productId);
    
    // Save cart
    localStorage.setItem('cart', JSON.stringify(cart));
    
    // Update cart count
    updateCartCount();
    
    // Update cart items
    updateCartItems();
    
    // Show notification
    showNotification('Product removed from cart');
}

// Increase quantity
function increaseQuantity(productId) {
    console.log('Increasing quantity for product:', productId);
    
    // Get current cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Find product in cart
    const productIndex = cart.findIndex(item => item.id === productId);
    
    if (productIndex !== -1) {
        // Increase quantity
        cart[productIndex].quantity += 1;
        
        // Save cart
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // Update cart items
        updateCartItems();
    }
}

// Decrease quantity
function decreaseQuantity(productId) {
    console.log('Decreasing quantity for product:', productId);
    
    // Get current cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Find product in cart
    const productIndex = cart.findIndex(item => item.id === productId);
    
    if (productIndex !== -1) {
        // Decrease quantity
        cart[productIndex].quantity -= 1;
        
        // If quantity is 0, remove product from cart
        if (cart[productIndex].quantity <= 0) {
            removeFromCart(productId);
            return;
        }
        
        // Save cart
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // Update cart items
        updateCartItems();
    }
}

// Make functions available globally
window.removeFromCart = removeFromCart;
window.increaseQuantity = increaseQuantity;
window.decreaseQuantity = decreaseQuantity;










