// Wait for DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM fully loaded');

    // Theme toggle functionality
    setupThemeToggle();

    // Load products
    setupProductFiltering();
});

/**
 * Theme System:
 * - All pages (client and admin) use the same 'theme' localStorage key
 * - This allows unified dark mode preference across the entire application
 */

// Setup theme toggle
function setupThemeToggle() {
    const themeToggle = document.getElementById('theme-toggle');
    console.log('Theme toggle element:', themeToggle);

    if (!themeToggle) {
        console.error('Theme toggle button not found');
        return;
    }

    const body = document.body;

    // Migration: Consolidate separate theme preferences back to unified 'theme'
    if (localStorage.getItem('client-theme') && !localStorage.getItem('theme')) {
        localStorage.setItem('theme', localStorage.getItem('client-theme'));
        localStorage.removeItem('client-theme'); // Clean up separate preference
    }
    if (localStorage.getItem('admin-theme') && !localStorage.getItem('theme')) {
        localStorage.setItem('theme', localStorage.getItem('admin-theme'));
        localStorage.removeItem('admin-theme'); // Clean up separate preference
    }

    // Check for saved theme preference (unified across all pages)
    if (localStorage.getItem('theme') === 'dark') {
        body.classList.add('dark-mode');
        themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
    }

    // Add click event listener
    themeToggle.addEventListener('click', function() {
        console.log('Theme toggle clicked');

        // Toggle dark mode class
        body.classList.toggle('dark-mode');

        // Update icon and save unified theme preference
        if (body.classList.contains('dark-mode')) {
            themeToggle.innerHTML = '<i class="fas fa-sun"></i>';
            localStorage.setItem('theme', 'dark');
        } else {
            themeToggle.innerHTML = '<i class="fas fa-moon"></i>';
            localStorage.setItem('theme', 'light');
        }
    });
}

// Setup product filtering
function setupProductFiltering() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const productsContainer = document.querySelector('.products-grid');
    
    if (filterBtns.length > 0 && productsContainer) {
        let category = 'all';
        
        filterBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                filterBtns.forEach(btn => btn.classList.remove('active'));
                
                // Add active class to clicked button
                this.classList.add('active');
                
                // Get category
                category = this.getAttribute('data-filter');
                
                // Load products
                loadProducts(category);
            });
        });
        
        // Load initial products
        loadProducts(category);
    } else {
        console.error('Filter buttons or products container not found');
    }
}

// Load products function
function loadProducts(category = 'all') {
    console.log('Loading products for category:', category);
    const productsGrid = document.querySelector('.products-grid');
    
    if (!productsGrid) {
        console.error('Products grid not found');
        return;
    }
    
    // Show loading indicator
    productsGrid.innerHTML = '<div class="loading">Loading products...</div>';
    
    // Fetch products from backend
    fetch(`backend/get_products.php${category !== 'all' ? '?category=' + category : ''}`)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(products => {
            console.log('Products loaded:', products);
            
            if (!products || products.length === 0) {
                productsGrid.innerHTML = '<div class="no-products">No products found in this category</div>';
                return;
            }
            
            // Generate products HTML
            let html = '';
            
            products.forEach(product => {
                html += `
                    <div class="product-card">
                        <div class="product-img">
                            <img src="${product.image}" alt="${product.name}">
                        </div>
                        <div class="product-info">
                            <h3>${product.name}</h3>
                            <div class="product-price">${product.price} DZD</div>
                            <button class="add-to-cart" onclick="addToCart(${product.id}, '${product.name}', ${product.price}, '${product.image}')">
                                <i class="fas fa-shopping-cart"></i> Add to Cart
                            </button>
                        </div>
                    </div>
                `;
            });
            
            productsGrid.innerHTML = html;
        })
        .catch(error => {
            console.error('Error loading products:', error);
            productsGrid.innerHTML = '<div class="error">Failed to load products. Please try again later.</div>';
        });
}

// Add to cart function with direct parameters
function addToCart(productId, productName, productPrice, productImage) {
    console.log('Adding product to cart:', productId, productName, productPrice, productImage);
    
    // Get current cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Check if product already in cart
    const existingProductIndex = cart.findIndex(item => item.id === productId);
    
    if (existingProductIndex !== -1) {
        // Product already in cart, show notification
        showNotification('Product already in cart', 'info');
    } else {
        // Add product to cart
        cart.push({
            id: productId,
            name: productName,
            price: productPrice,
            image: productImage,
            quantity: 1
        });
        
        // Save cart
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // Show success notification
        showNotification('Product added to cart', 'success');
    }
    
    // Update cart count
    updateCartCount();
}

// Update cart count
function updateCartCount() {
    const cartCount = document.querySelector('.cart-count');
    
    if (!cartCount) return;
    
    const cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Count unique products, not total items
    const uniqueProducts = cart.length;
    
    cartCount.textContent = uniqueProducts;
}

// Show notification
function showNotification(message, type = 'success') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    // Add to body
    document.body.appendChild(notification);
    
    // Show notification
    setTimeout(() => {
        notification.classList.add('show');
    }, 10);
    
    // Hide notification after 3 seconds
    setTimeout(() => {
        notification.classList.remove('show');
        
        // Remove from DOM after animation
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Remove from cart
function removeFromCart(productId) {
    console.log('Removing product from cart:', productId);
    
    // Get current cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Remove product from cart
    cart = cart.filter(item => item.id !== productId);
    
    // Save cart
    localStorage.setItem('cart', JSON.stringify(cart));
    
    // Update cart count
    updateCartCount();
    
    // Update cart items
    updateCartItems();
    
    // Show notification
    showNotification('Product removed from cart');
}

// Increase quantity
function increaseQuantity(productId) {
    console.log('Increasing quantity for product:', productId);
    
    // Get current cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Find product in cart
    const productIndex = cart.findIndex(item => item.id === productId);
    
    if (productIndex !== -1) {
        // Increase quantity
        cart[productIndex].quantity += 1;
        
        // Save cart
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // Update cart items
        updateCartItems();
    }
}

// Decrease quantity
function decreaseQuantity(productId) {
    console.log('Decreasing quantity for product:', productId);
    
    // Get current cart
    let cart = JSON.parse(localStorage.getItem('cart')) || [];
    
    // Find product in cart
    const productIndex = cart.findIndex(item => item.id === productId);
    
    if (productIndex !== -1) {
        // Decrease quantity
        cart[productIndex].quantity -= 1;
        
        // If quantity is 0, remove product from cart
        if (cart[productIndex].quantity <= 0) {
            removeFromCart(productId);
            return;
        }
        
        // Save cart
        localStorage.setItem('cart', JSON.stringify(cart));
        
        // Update cart items
        updateCartItems();
    }
}

// Make functions available globally
window.removeFromCart = removeFromCart;
window.increaseQuantity = increaseQuantity;
window.decreaseQuantity = decreaseQuantity;










