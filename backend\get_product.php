<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Get product ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$id) {
    json_response(['success' => false, 'message' => 'Invalid product ID']);
}

try {
    // Get product
    $stmt = $db->prepare("SELECT * FROM products WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        json_response(['success' => false, 'message' => 'Product not found']);
    }
    
    json_response($product);
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error fetching product: ' . $e->getMessage()]);
}


