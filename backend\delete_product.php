<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Get product ID
$id = isset($_GET['id']) ? intval($_GET['id']) : 0;

if (!$id) {
    json_response(['success' => false, 'message' => 'Invalid product ID']);
}

try {
    // Start transaction
    $db->beginTransaction();
    
    // Get product image path
    $stmt = $db->prepare("SELECT image FROM products WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    
    $product = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$product) {
        json_response(['success' => false, 'message' => 'Product not found']);
    }
    
    // Delete product
    $stmt = $db->prepare("DELETE FROM products WHERE id = :id");
    $stmt->bindParam(':id', $id);
    $stmt->execute();
    
    // Commit transaction
    $db->commit();
    
    // Delete product image if not default
    if ($product['image'] !== 'uploads/default-product.jpg') {
        $imagePath = '../' . $product['image'];
        if (file_exists($imagePath)) {
            unlink($imagePath);
        }
    }
    
    json_response(['success' => true, 'message' => 'Product deleted successfully']);
} catch(PDOException $e) {
    // Rollback transaction on error
    $db->rollBack();
    json_response(['success' => false, 'message' => 'Error deleting product: ' . $e->getMessage()]);
}

