<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Get request body
$data = json_decode(file_get_contents('php://input'), true);

// Validate data
if (!isset($data['wilaya_code']) || !isset($data['domicile']) || !isset($data['stopdesk'])) {
    json_response(['success' => false, 'message' => 'Missing required fields']);
}

$wilayaCode = sanitize($data['wilaya_code']);
$domicile = intval($data['domicile']);
$stopdesk = intval($data['stopdesk']);

// Validate values
if ($domicile < 0 || $stopdesk < 0) {
    json_response(['success' => false, 'message' => 'Delivery costs cannot be negative']);
}

try {
    // Check if delivery cost exists
    $stmt = $db->prepare("SELECT COUNT(*) FROM delivery_costs WHERE wilaya_code = :code");
    $stmt->bindParam(':code', $wilayaCode);
    $stmt->execute();
    
    $exists = $stmt->fetchColumn() > 0;
    
    if ($exists) {
        // Update existing delivery cost
        $stmt = $db->prepare("UPDATE delivery_costs SET domicile = :domicile, stopdesk = :stopdesk WHERE wilaya_code = :code");
    } else {
        // Get wilaya name from code
        $wilayaNames = [
            '1' => 'ADRAR', '2' => 'CHLEF', '3' => 'LAGHOUAT', '4' => 'OUM EL BOUAGHI',
            '5' => 'BATNA', '6' => 'BEJAIA', '7' => 'BISKRA', '8' => 'BECHAR',
            '9' => 'BLIDA', '10' => 'BOUIRA', '11' => 'TAMANRASSET', '12' => 'TEBESSA',
            '13' => 'TLEMCEN', '14' => 'TIARET', '15' => 'TIZI OUZOU', '16' => 'ALGER',
            '17' => 'DJELFA', '18' => 'JIJEL', '19' => 'SETIF', '20' => 'SAIDA',
            '21' => 'SKIKDA', '22' => 'SIDI BEL ABBES', '23' => 'ANNABA', '24' => 'GUELMA',
            '25' => 'CONSTANTINE', '26' => 'MEDEA', '27' => 'MOSTAGANEM', '28' => 'M\'SILA',
            '29' => 'MASCARA', '30' => 'OUARGLA', '31' => 'ORAN', '32' => 'EL BAYADH',
            '34' => 'BORDJ BOU ARRERIDJ', '35' => 'BOUMERDES', '36' => 'EL TARF', '38' => 'TISSEMSILT',
            '39' => 'EL OUED', '40' => 'KHENCHELA', '41' => 'SOUK AHRAS', '42' => 'TIPAZA',
            '43' => 'MILA', '44' => 'AIN DEFLA', '45' => 'NAAMA', '46' => 'AIN TEMOUCHENT',
            '47' => 'GHARDAIA', '48' => 'RELIZANE', '49' => 'TIMIMOUN', '51' => 'OULED DJELLAL',
            '52' => 'BENI ABBES', '53' => 'IN SALAH', '54' => 'IN GUEZZAM', '55' => 'TOUGGOURT',
            '57' => 'M\'GHAIR', '58' => 'EL MENIA'
        ];
        
        $wilayaName = isset($wilayaNames[$wilayaCode]) ? $wilayaNames[$wilayaCode] : 'WILAYA ' . $wilayaCode;
        
        // Insert new delivery cost
        $stmt = $db->prepare("INSERT INTO delivery_costs (wilaya_code, wilaya_name, domicile, stopdesk) VALUES (:code, :name, :domicile, :stopdesk)");
        $stmt->bindParam(':name', $wilayaName);
    }
    
    $stmt->bindParam(':code', $wilayaCode);
    $stmt->bindParam(':domicile', $domicile);
    $stmt->bindParam(':stopdesk', $stopdesk);
    $stmt->execute();
    
    json_response(['success' => true, 'message' => 'Delivery cost updated successfully']);
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error updating delivery cost: ' . $e->getMessage()]);
}