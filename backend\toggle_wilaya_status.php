<?php
require_once 'config.php';

// Check admin login
check_admin_login();

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    json_response(['success' => false, 'message' => 'Invalid request method']);
}

// Get wilaya code
$wilaya_code = isset($_POST['wilaya_code']) ? sanitize($_POST['wilaya_code']) : '';

// Validate wilaya code
if (empty($wilaya_code)) {
    json_response(['success' => false, 'message' => 'Wilaya code is required']);
}

try {
    // Get current status
    $stmt = $db->prepare("SELECT active, wilaya_name FROM delivery_costs WHERE wilaya_code = :wilaya_code");
    $stmt->bindParam(':wilaya_code', $wilaya_code);
    $stmt->execute();
    
    $wilaya = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$wilaya) {
        json_response(['success' => false, 'message' => 'Wilaya not found']);
    }
    
    // Toggle status
    $newStatus = $wilaya['active'] ? 0 : 1;
    
    // Update status
    $stmt = $db->prepare("UPDATE delivery_costs SET active = :active WHERE wilaya_code = :wilaya_code");
    $stmt->bindParam(':active', $newStatus);
    $stmt->bindParam(':wilaya_code', $wilaya_code);
    $stmt->execute();
    
    if ($stmt->rowCount() > 0) {
        $statusText = $newStatus ? 'activated' : 'deactivated';
        json_response([
            'success' => true, 
            'message' => "Wilaya {$wilaya['wilaya_name']} {$statusText} successfully",
            'new_status' => $newStatus
        ]);
    } else {
        json_response(['success' => false, 'message' => 'Failed to update wilaya status']);
    }
    
} catch(PDOException $e) {
    json_response(['success' => false, 'message' => 'Error updating wilaya status: ' . $e->getMessage()]);
}
?>
